<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar title="账号管理"></CustomNavBar>
    <view class="setting">
      <view class="setting-list border-b">
        <view class="flex-between">
          <view class="list-item-text text-32rpx">修改手机号</view>
          <view class="subText flex-c">
            <view class="p-r-10rpxrpx">177 **** **77</view>
            <wd-icon
              class="arrow-right-icon"
              color="#888888"
              name="chevron-right"
              size="20px"
            ></wd-icon>
          </view>
        </view>

        <view class="subText">修改成功后，可通过新手机号登录易直聘</view>
      </view>

      <view class="setting-list flex-between border-b">
        <view class="list-item-text text-32rpx">修改密码</view>

        <wd-icon
          class="arrow-right-icon"
          color="#888888"
          name="chevron-right"
          size="20px"
        ></wd-icon>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

const goAccountNumber = () => {
  uni.navigateTo({
    url: '/setting/accountNumber/index',
  })
}
</script>
<style lang="scss" scoped>
// 样式
.setting {
  padding: 0rpx 40rpx;

  .setting-list {
    padding: 30rpx 20rpx;

    .list-item-text {
      color: #333;
    }
  }
}

.setting-text {
  padding-bottom: 40rpx;
  font-size: 22rpx;
  color: #888888;
  border-bottom: 2rpx solid #d7d6d6 !important;
}
</style>
