import { UPLOAD } from '../index'
import { uploadImgThrumPrivateInt, uploadImgThrumInt } from './types'
import { HttpRequestConfig } from 'luch-request'

/** 私有文件类上传 */
export const uploadImgThrumPrivate = (config: HttpRequestConfig<UniApp.UploadTask>) =>
  UPLOAD<uploadImgThrumPrivateInt[]>('/easyzhipin-api/attachment/uploadImgThumPrivate', config)

// 共有文件类上传
export const uploadImgThrumPublic = (config: HttpRequestConfig<UniApp.UploadTask>) =>
  UPLOAD<uploadImgThrumPrivateInt[]>('/easyzhipin-api/attachment/uploadImgThum', config)
/** 图片压缩分辨率(OSS时不压缩)上传 */
export const uploadImgThrum = (config: HttpRequestConfig<UniApp.UploadTask>) =>
  UPLOAD<uploadImgThrumInt[]>('/easyzhipin-api/attachment/uploadImgThum', config)
