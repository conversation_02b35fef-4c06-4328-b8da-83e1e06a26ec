import { PropCardType } from '@/enum'

export interface payPrePayDataInt {
  /** 支付通道 0微信 1支付宝 */
  payPass: number
  /** 职位id */
  positionInfoId: number
  /** 道具类型id */
  propCategoryId: number
  /** 道具id(岗位是5) */
  propId: PropCardType
  /** 类型 0职位 1道具 */
  propType: number
  /** 优惠券id */
  userCouponId?: number
}

export interface payPrePayInt {
  /** 微信-appid */
  appid?: string
  /** 微信-noncestr */
  noncestr?: string
  /** 支付宝支付orderStr */
  orderStr?: string
  /** 业务订单号 */
  outTradeNo?: string
  /** 微信package */
  package?: string
  /** 微信-商户号 */
  partnerid?: string
  /** 微信-预下单id */
  prepayid?: string
  /** 微信-sign */
  sign?: string
  /** 微信-timestamp */
  timestamp?: string
}

export interface payQueryDealStatusDataInt extends Pick<payPrePayInt, 'outTradeNo'> {}
