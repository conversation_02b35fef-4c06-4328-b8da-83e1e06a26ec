<template>
  <z-paging ref="pagingRef" :paging-style="pageStyle" layout-only>
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          :bordered="false"
          custom-class="!bg-transparent px-30rpx"
          placeholder
          safe-area-inset-top
        >
          <template #left>
            <view class="page_flex_left">聊天</view>
          </template>
          <template #right>
            <view class="flex items-center" @click="goInfo">
              <image class="chartIcon" src="/static/img/Group_1171275033.png" />
            </view>
          </template>
        </wd-navbar>
        <view class="px-40rpx mt-28rpx">
          <wd-search
            v-model="searchConversation"
            custom-class="!p-0 !bg-transparent"
            hide-cancel
            placeholder="搜索联系人"
            placeholder-left
          />
          <view class="border-t-1px border-t-solid border-t-[#DEDDDD] ml--60rpx px-40rpx my-34rpx">
            <view class="w-580rpx">
              <wd-tabs v-model="newsTabsStatus" line-height="10rpx" line-width="80rpx">
                <wd-tab
                  v-for="(item, index) in newsTabsList"
                  :key="item.name"
                  :name="index"
                  :title="`${item.label}`"
                />
              </wd-tabs>
            </view>
          </view>
        </view>
      </wd-config-provider>
    </template>
    <!-- 聊天列表 -->
    <view class="px-40rpx pb-20rpx">
      <newsPersonalList
        v-model:search="searchConversation"
        :type="currentMarkType"
        @chat="handleChat"
      />
    </view>
    <template #bottom>
      <custom-tabbar name="news" />
    </template>
  </z-paging>
  <wd-toast />
  <wd-message-box />
</template>

<script lang="ts" setup>
import { CommonUtil, useMessage } from 'wot-design-uni'
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import { CONVERSATION_MARKS } from '@/enum'
import customTabbar from '@/components/common/custom-tabbar.vue'
import newsPersonalList from '@/components/news/news-personal-list.vue'

defineOptions({
  name: 'NewsPersonal',
})
const message = useMessage()
const { userRoleIsRealName } = useUserInfo()
const { pagingRef, pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 140deg, #FFC8C8 0%, #EDF1FF 22%, #FFFFFF 100%)',
  },
})
const themeVars: ConfigProviderThemeVars = {
  searchInputBg: '#FFFFFF',
  searchInputHeight: '82rpx',
  searchInputRadius: '80rpx',
  searchPlaceholderColor: '#333333',
  searchIconSize: '36rpx',
  searchIconColor: '#333333',
  searchInputFs: '24rpx',
  searchInputColor: ' #333333',
  searchCancelColor: '#333333',
  tabsNavLineBgColor: '#FFA7A7',
  tabsNavFs: '26rpx',
}
const searchConversation = ref('')

const newsTabsStatus = ref(0)
const newsTabsList = [
  {
    label: '全部',
    name: CONVERSATION_MARKS.ALL,
    index: 0,
  },
  {
    label: '新招呼',
    name: CONVERSATION_MARKS.NEW_GREETING,
    index: 1,
  },
  {
    label: '仅沟通',
    name: CONVERSATION_MARKS.ONLY_CHAT,
    index: 2,
  },
  {
    label: '有交换',
    name: CONVERSATION_MARKS.EXCHANGED,
    index: 3,
  },
]
// 当前选中的标记类型
const currentMarkType = computed(() => {
  return newsTabsList[newsTabsStatus.value]?.name ?? CONVERSATION_MARKS.ALL
})
const goInfo = () => {
  uni.navigateTo({
    url: '/chartPage/message/index',
  })
}
const userToRealName = () => {
  if (!userRoleIsRealName.value) {
    message
      .confirm({
        title: '提示',
        msg: '请先实名认证',
      })
      .then(() => {
        uni.navigateTo({
          url: '/setting/identityAuth/index',
        })
      })
      .catch()
    return Promise.reject(new Error('请先实名认证'))
  }
  return Promise.resolve(1)
}

const handleChat = async (id: string) => {
  try {
    await userToRealName()
    uni.navigateTo({
      url: CommonUtil.buildUrlWithParams('/ChatUIKit/modules/Chat/index', {
        type: 'singleChat',
        id,
      }),
    })
  } catch (error) {}
}
</script>

<style lang="scss" scoped>
:deep(.wd-tabs) {
  background: transparent;

  .wd-tabs__line {
    bottom: 6px;
  }

  .wd-tabs__nav {
    background: transparent;
  }

  .wd-tabs__nav-item {
    &.is-active {
      font-size: 30rpx;
    }
  }
}

.chartIcon {
  width: 50rpx;
  height: 50rpx;
}

.page_flex_left {
  position: relative;
  padding-top: 20rpx;
  padding-bottom: 10rpx;
  font-size: 60rpx;
  font-weight: 600;
  color: #000000;
}

.page_flex_left::after {
  position: absolute;
  bottom: 10rpx;
  left: 0rpx;
  width: 170rpx;
  height: 12rpx;
  content: '';
  background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
  border-radius: 32rpx;
}
</style>
