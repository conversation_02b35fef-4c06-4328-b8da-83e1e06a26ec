import { companyApply } from '@/service/common'

export const useExamine = () => {
  const { setHrExamineStateObj } = useUserInfo()
  const getExamineState = async () => {
    console.log('getExamineState==========')
    try {
      const { data } = await companyApply({
        custom: {
          catch: true,
        },
      })
      // console.log(data, 'data==========审核信息')
      setHrExamineStateObj({
        auditStatus: data.auditStatus,
        status: data.status,
      })
    } catch (error) {
      return Promise.reject(error)
    }
  }
  return {
    getExamineState,
  }
}
