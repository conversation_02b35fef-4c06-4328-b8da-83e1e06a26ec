<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar title="登录账号管理"></CustomNavBar>
    <view class="setting">
      <view class="setting-text m-b-40rpxrpx">
        以下是最近登录过您账号的设备情况，若发现非本人操作，请及时删除，以保障您的账号安全
      </view>

      <view
        v-if="currentDevice && (currentDevice.deviceBrand || currentDevice.deviceModel)"
        class="setting-list border-b"
      >
        <view class="flex-between">
          <view class="text-32rpx font-w-500">
            {{ `${currentDevice?.deviceBrand || ''} ${currentDevice?.deviceModel || ''}` }}
          </view>
          <view class="color-8">当前设备</view>
        </view>
        <view class="color-8">{{ currentDevice?.updateTime || '' }}</view>
      </view>
      <template v-if="deviceBrandList && deviceBrandList.length > 0">
        <view v-for="(item, index) in deviceBrandList" :key="index" class="setting-list border-b">
          <view class="flex-between">
            <view class="text-32rpx font-w-500">
              {{ `${item?.deviceBrand || ''} ${item?.deviceModel || ''}` }}
            </view>
          </view>
          <view class="color-8">{{ item?.updateTime || '' }}</view>
        </view>
      </template>
    </view>
  </view>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { queryLoginDeviceList } from '@/interPost/my'

const deviceBrandList = ref<any>([]) // 历史登录设备品牌列表
const currentDevice = ref<any>({}) // 当前设备信息

const loginDeviceList = async () => {
  const res: any = await queryLoginDeviceList()
  if (res.code === 0 && Array.isArray(res.data)) {
    const now = new Date().getTime()

    // 按时间差从小到大排序（时间越接近现在的排前面）
    const sortedList = res.data.sort((a: any, b: any) => {
      const diffA = Math.abs(new Date(a.updatatime).getTime() - now)
      const diffB = Math.abs(new Date(b.updatatime).getTime() - now)
      return diffA - diffB
    })

    deviceBrandList.value = sortedList || []
    currentDevice.value = sortedList[0] || {}
  }
}

onLoad(async () => {
  await uni.$onLaunched
  loginDeviceList()
})
</script>
<style lang="scss" scoped>
.setting {
  padding: 40rpx 40rpx;

  .setting-list {
    padding: 30rpx 0rpx;
  }
}

::v-deep .cell-set .u-line {
  border-bottom: 2rpx solid #d7d6d6 !important;
}

::v-deep .u-cell__title-text {
  font-size: 32rpx !important;
  font-weight: 500;
  color: #333;
}

::v-deep .u-cell__body--large {
  padding: 40rpx 0rpx 40rpx;
}

::v-deep .u-cell__value {
  font-size: 28rpx;
  color: #888888;
}

::v-deep .u-cell__label--large {
  font-size: 28rpx;
}

::v-deep .u-cell--clickable {
  background-color: transparent !important;
}

.setting-text {
  font-size: 24rpx;
  color: #888888;
}
</style>
