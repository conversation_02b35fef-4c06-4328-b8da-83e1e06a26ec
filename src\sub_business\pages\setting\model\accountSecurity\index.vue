<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging :paging-style="pageStyle" layout-only>
    <template #top>
      <CustomNavBar title="账号安全"></CustomNavBar>
    </template>
    <view class="setting">
      <!-- 修改手机号 -->
      <view class="setting-list border-b" @click="goPhoneChange">
        <view class="row-main flex-between">
          <view class="list-item-text text-32rpx">修改手机号</view>
          <view class="phone-arrow">
            <text class="phone-text">{{ maskedPhone }}</text>
            <wd-icon
              class="arrow-right-icon"
              color="#888888"
              name="chevron-right"
              size="20px"
            ></wd-icon>
          </view>
        </view>
        <view class="tip-text">修改成功后，可通过新手机号登录易直聘</view>
      </view>
      <!-- 修改密码 -->
      <view class="setting-list border-b" @click="goPasswordChange">
        <view class="row-main flex-between pb-20rpx">
          <view class="text-32rpx c-#333333">修改密码</view>
          <wd-icon
            class="arrow-right-icon"
            color="#888888"
            name="chevron-right"
            size="20px"
          ></wd-icon>
        </view>
      </view>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { useUserStore } from '@/store'
import { maskPhoneNumber } from '@/utils/common'

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

// 获取用户store
const userStore = useUserStore()

// 计算脱敏后的手机号
const maskedPhone = computed(() => {
  const phone = userStore.userInfo?.phone
  if (phone) {
    return maskPhoneNumber(phone)
  }
  return '未设置'
})

const goPhoneChange = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/accountSecurity/PhoneChange',
  })
}
// 修改密码
const goPasswordChange = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/accountSecurity/PasswordChange',
  })
}
</script>
<style lang="scss" scoped>
.setting {
  padding: 0rpx 40rpx;

  .setting-list {
    padding: 30rpx 20rpx 12rpx 20rpx;

    .list-item-text {
      color: #333;
    }
  }

  .row-main {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .phone-arrow {
    display: flex;
    align-items: center;

    .phone-text {
      margin-right: 10rpx;
      font-size: 28rpx;
      color: #888;
    }
  }

  .tip-text {
    margin-top: 8rpx;
    margin-left: 2rpx;
    font-size: 22rpx;
    line-height: 1.4;
    color: #999999;
  }
}
</style>
