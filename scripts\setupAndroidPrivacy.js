const fs = require('fs-extra')
const path = require('path')

/**
 * 设置 Android 隐私配置
 * 将 androidPrivacy.json 复制到正确的位置
 */
async function setupAndroidPrivacy() {
  const sourcePath = path.resolve(__dirname, '../src/androidPrivacy.json')
  const targetPath = path.resolve(__dirname, '../dist/build/app-plus/androidPrivacy.json')

  try {
    // 检查源文件是否存在
    const sourceExists = await fs.pathExists(sourcePath)
    if (!sourceExists) {
      console.error('❌ androidPrivacy.json 文件不存在:', sourcePath)
      return false
    }

    // 确保目标目录存在
    await fs.ensureDir(path.dirname(targetPath))

    // 复制文件
    await fs.copy(sourcePath, targetPath)
    console.log('✅ androidPrivacy.json 已复制到:', targetPath)

    // 验证复制是否成功
    const targetExists = await fs.pathExists(targetPath)
    if (targetExists) {
      console.log('✅ Android 隐私配置设置完成')
      return true
    } else {
      console.error('❌ 文件复制失败')
      return false
    }
  } catch (error) {
    console.error('❌ 设置 Android 隐私配置失败:', error)
    return false
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  setupAndroidPrivacy()
}

module.exports = { setupAndroidPrivacy }
