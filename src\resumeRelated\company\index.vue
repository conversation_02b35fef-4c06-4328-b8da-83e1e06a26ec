<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="conpany-img">
    <z-paging :loading-more-enabled="false" :refresher-enabled="false">
      <template #top>
        <CustomNavBar color="#fff">
          <template v-if="!isPreview" #right>
            <wd-img
              v-if="listObj.collectStatus === 0"
              :height="22"
              :src="collect"
              :width="22"
              @click="collectFun"
            />
            <wd-img
              v-if="listObj.collectStatus === 1"
              :height="22"
              :src="noCollect"
              :width="22"
              @click="nocollectFun"
            />
          </template>
        </CustomNavBar>
      </template>
      <view class="company-list">
        <view class="flex-c">
          <!-- <image src="/static/img/1.jpg" class="company-logos"></image> -->
          <image
            :src="listObj.logoUrl ? listObj.logoUrl : '/static/header/logo.png'"
            class="company-logos"
            mode="aspectFill"
          ></image>
          <view class="company-title">
            <view class="company-name">{{ listObj.companyName }}</view>
            <view class="flex-c">
              <!-- <view class="white-color font-style p-r-10rpx text-22rpx">B轮融资</view> -->
              <view class="white-color font-style p-r-10rpx text-22rpx">
                {{ listObj.industryName }}
              </view>
              <view v-if="listObj.sizeName" class="white-color company-num font-style text-22rpx">
                {{ listObj.sizeName }}
              </view>
            </view>
            <!-- <view class="flex-c"> -->
            <!-- <view class="white-color text-22rpx p-r-10rpx">B互联网</view> -->
            <!-- <view class="white-color text-22rpx">{{ listObj.industryName }}</view> -->
            <!-- </view> -->
          </view>
        </view>
        <view class="company-js">
          <wd-tabs
            v-model="tab"
            class="transparent-tabs"
            color="#F4BB86"
            inactiveColor="#fff"
            lineWidth="76rpx"
            slidable="always"
          >
            <!-- 公司介绍 -->
            <wd-tab v-if="textyl" name="公司介绍" title="公司介绍">
              <view class="tab-content">
                <view class="text-container">
                  <text class="text-content white-color text-28rpx">{{ text }}</text>
                  <text
                    v-if="showMore"
                    class="view-detail text-24rpx"
                    style="color: #f4bb86"
                    @click="showDetail"
                  >
                    查看详情
                  </text>
                  <text
                    v-if="showText && !showMore"
                    class="view-detail text-24rpx"
                    style="color: #f4bb86"
                    @click="showShrink"
                  >
                    收起
                  </text>
                </view>
              </view>
            </wd-tab>

            <!-- 福利待遇 -->
            <wd-tab
              v-if="displayedBenefits && displayedBenefits.length > 0"
              name="福利待遇"
              title="福利待遇"
            >
              <view class="tab-content company-benefit">
                <view class="benefit-grid">
                  <view
                    v-for="(item, index) in displayedBenefits"
                    :key="index"
                    class="benefit-grid-item"
                  >
                    <view class="benefit-item-content">
                      <image
                        :src="getBenefitsIcon(item.welfareTreatment)"
                        class="benefit-list-img"
                      ></image>
                      <view class="white-color benefit-list-name text-28rpx">
                        {{ item.welfareTreatment }}
                      </view>
                    </view>
                  </view>
                </view>
                <!-- 查看详情/收起按钮 -->
                <view
                  v-if="listObj.companyWelfareVOList && listObj.companyWelfareVOList.length > 4"
                  class="benefit-toggle-btn"
                  @click="toggleBenefits"
                >
                  <text class="toggle-text">{{ benefitsExpanded ? '收起' : '查看更多' }}</text>
                  <image :src="benefitsExpanded ? closeIcon : expandIcon" class="toggle-icon-img" />
                </view>
              </view>
            </wd-tab>

            <!-- 公司照片 -->
            <wd-tab
              v-if="listObj.companyStyleVOLists && listObj.companyStyleVOLists.length > 0"
              name="公司照片"
              title="公司照片"
            >
              <view class="tab-content">
                <view class="card-swiper">
                  <wd-swiper
                    v-model:current="current"
                    :autoplay="false"
                    :indicator="false"
                    :list="listObj.companyStyleVOLists"
                    custom-image-class="custom-image"
                    custom-indicator-class="custom-indicator-class"
                    custom-next-image-class="custom-image-prev"
                    custom-prev-image-class="custom-image-prev"
                    height="260rpx"
                    nextMargin="44px"
                    previousMargin="44px"
                    value-key="attachIdUrl"
                  ></wd-swiper>
                </view>
              </view>
            </wd-tab>

            <!-- 工商信息 -->
            <wd-tab name="工商信息" title="工商信息">
              <view class="tab-content">
                <view v-if="listObj.companyName" class="white-color line-20 text-28rpx">
                  公司全称：{{ listObj.companyName }}
                </view>
                <view v-if="listObj.legalPerson" class="white-color line-20 text-28rpx">
                  法定代表人：{{ listObj.legalPerson }}
                </view>
                <view v-if="listObj.registeredCapital" class="white-color line-20 text-28rpx">
                  注册资本：{{ listObj.registeredCapital }}
                </view>
                <view v-if="listObj.registrationDate" class="white-color line-20 text-28rpx">
                  成立日期：{{ listObj.registrationDate }}
                </view>
                <!-- 公司地址部分 -->
                <view class="company-adress">
                  <view class="text-32rpx white-color">公司地址</view>
                  <view class="flex-between">
                    <view class="white-color text-24rpx address-container">
                      <view class="address-line">{{ listObj.regAddress }}</view>
                    </view>
                    <view class="m-l-20rpx" @click="goMap">
                      <view class="company-adress-img">
                        <view class="company-adress-icon"></view>
                      </view>
                      <!-- <view class="white-color text-c text-22rpx p-t-10rpx">导航</view> -->
                    </view>
                  </view>
                </view>
              </view>
            </wd-tab>
          </wd-tabs>
        </view>
      </view>
      <view v-if="!isPreview" class="company-list company-list-job m-t--20rpx">
        <view class="flex-between p-b-20rpx">
          <view class="text-32rpx white-color">更多岗位</view>
          <view class="view-all-btn" @click="goAllJobs">
            <text class="view-all-text">查看全部</text>
            <text class="view-all-arrow">
              <wd-icon
                class="arrow-right-1"
                color="#B6B6B6"
                name="chevron-right"
                size="15px"
              ></wd-icon>
            </text>
          </view>
        </view>
        <scroll-view class="job-scroll-view" scroll-x>
          <view class="job-list">
            <view
              v-for="(item, index) in jobList"
              :key="index"
              class="job-card"
              @click="goJobDetail(item.id)"
            >
              <view class="job-card-content">
                <view class="job-info-row">
                  <image :src="jobListIcon[0]" class="job-icon"></image>
                  <view class="job-title white-color text-28rpx">
                    {{
                      item.positionMarkName
                        ? (item.positionMarkName ?? '视频运营总监电商运营')
                        : (item.positionName ?? '视频运营总监电商运营')
                    }}
                  </view>
                </view>
                <view class="job-info-row">
                  <image :src="jobListIcon[1]" class="job-icon"></image>
                  <view class="job-salary white-color text-24rpx">
                    <template v-if="!item.workSalaryBegin || !item.workSalaryEnd">面议</template>
                    <template v-else>
                      {{
                        `${formatSalary(item.workSalaryBegin)} -
                        ${formatSalary(item.workSalaryEnd)}/月`
                      }}
                      <text v-if="item.salaryMonths">·{{ item.salaryMonths }}薪</text>
                    </template>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { handleNavigation } from '@/utils/open-map-url'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { getCustomBar } from '@/utils/storage'
import { numberTokw } from '@/utils/common'
import {
  queryAllDetailId,
  cancelCompany,
  collectCompany,
  queryPositionByCompanyId,
} from '@/interPost/home'
import { baseUrlPrever } from '@/interPost/img'
import { hrCompany } from '@/service/hrDetail'

import collect from '@/static/img/collect.png'
import noCollect from '@/static/img/noCollect.png'
import salaryIcon from '@/static/img/salary_icon.png'
import toolkitIcon from '@/static/img/toolkit_icon.png'
import expandIcon from '@/resumeRelated/img/expand_icon.png'
import closeIcon from '@/resumeRelated/img/close_icon.png'
import { useBenefitsIcon } from '@/sub_business/hooks/useBenefitsIcon'

// 导入福利图标hooks
const { getBenefitsIcon } = useBenefitsIcon()

const { pagingRef, pageInfo, pageData, pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

const { getDictLabel } = useDictionary()
const isPreview = ref(false)
// 公司id
const id = ref(null)
// 主键id
const idMain = ref(null)
// 列表
const listObj = ref<AnyObject>({})
// 岗位列表
const jobList = ref<any>([])
const content = ref(
  '重庆某某某科技股份有限公司创建于2015年，位于重庆市渝北区华润大厦49楼4901，注册资金1000万，在职总共员工200余人，同时在深圳、成都、福州等地区都设有分公司',
)
const customBar = ref(0)
const circular = ref(true)
const current = ref(0)
const text = ref('') // 剪切之后的文字
const textyl = ref('') // 原来的字符
const showMore = ref(false) // 判断显示查看或者收起   小于40字不展示
const textlength = ref(false) // 判断是否点击详情 true为点击详情 false为点击收起
const showText = ref(false) // 收起文字的显示隐藏
const lon = ref(null)
const lat = ref(null)

const jobListIcon = ref([toolkitIcon, salaryIcon])
const tab = ref('公司介绍') // 当前选中的标签页索引

// 福利待遇展开/收起相关
const benefitsExpanded = ref(false) // 福利待遇是否展开

// 格式化薪资，将元转换为K单位
const formatSalary = (salary: number) => {
  if (salary === 0) return '面议'
  return numberTokw(salary)
}

// 计算显示的福利待遇列表
const displayedBenefits = computed(() => {
  if (!listObj.value.companyWelfareVOList) return []

  const benefits = listObj.value.companyWelfareVOList
  if (benefits.length <= 6) {
    return benefits
  }

  return benefitsExpanded.value ? benefits : benefits.slice(0, 6)
})

// 切换福利待遇展开/收起状态
const toggleBenefits = () => {
  benefitsExpanded.value = !benefitsExpanded.value
}

// 获取列表
const getList = async () => {
  const res: any = isPreview.value
    ? await hrCompany()
    : await queryAllDetailId({
        id: id.value,
      })
  if (res.code === 0) {
    textyl.value = res.data.profile // 保存原始文本
    res.data.sizeName = await getDictLabel(100, res.data.sizeName)
    listObj.value = res.data
    lon.value = res.data.lon
    lat.value = res.data.lat
    // 在获取数据后立即检查文本长度并设置显示内容
    checkTextLength()
  }
}
// 获取全部岗位
const getAllJobs = async () => {
  const res: any = await queryPositionByCompanyId({
    entity: {
      id: id.value,
      positionName: '',
    },
    size: pageInfo.size,
    page: pageInfo.page,
  })
  // 处理岗位数据
  if (res.code === 0) {
    jobList.value = res.data.list.slice(0, 4) || []
  }
}
// 收藏
const collectFun = async () => {
  const res: any = await collectCompany({ id: id.value })
  if (res.code === 0) {
    listObj.value.collectStatus = 1
  }
}
// 取消收藏
const nocollectFun = async () => {
  const res: any = await cancelCompany({ id: id.value })
  if (res.code === 0) {
    listObj.value.collectStatus = 0
  }
}
const goMap = () => {
  handleNavigation({
    latitude: listObj.value.lat,
    longitude: listObj.value.lon,
    name: listObj.value.companyName,
  })
  // uni.openLocation({
  //   latitude: Number(lat.value), // 目标纬度
  //   longitude: Number(lon.value), // 目标经度
  //   name: listObj.value.companyName, // 显示在地图上的标记名称
  //   address: listObj.value.regAddress, // 辅助信息
  //   success: () => console.log('跳转成功'),
  //   fail: (err) => console.error('跳转失败', err),
  // })
}

// 跳转到岗位详情
const goJobDetail = (jobId: any) => {
  uni.navigateTo({
    url: `/resumeRelated/jobDetail/index?id=${jobId}&companyId=${id.value}`,
  })
}

// 查看全部岗位
const goAllJobs = () => {
  uni.navigateTo({
    url: `/resumeRelated/moreJobs/index?companyId=${id.value}`,
  })
}
onLoad(async (options) => {
  await uni.$onLaunched
  customBar.value = getCustomBar()

  if (options.companyId) {
    id.value = options.companyId
    idMain.value = options.id
    getAllJobs()
    isPreview.value = false
  } else {
    isPreview.value = true
  }
  getList()
  // checkTextLength() 已经在 getList() 中调用，这里不需要重复调用
})
const checkTextLength = () => {
  // 如果原始文本长度大于110字符
  if (textyl.value && textyl.value.length > 110) {
    if (!textlength.value) {
      // 默认显示简洁内容（前110个字符 + ...）
      text.value = textyl.value.substring(0, 110) + '...'
      showMore.value = true // 显示"查看详情"按钮
      showText.value = false // 隐藏"收起"按钮
    } else {
      // 显示完整内容
      text.value = textyl.value
      showMore.value = false // 隐藏"查看详情"按钮
      showText.value = true // 显示"收起"按钮
    }
  } else {
    // 文本长度不超过100字符，直接显示全部内容，不显示任何按钮
    text.value = textyl.value
    showMore.value = false
    showText.value = false
  }
}
const showDetail = () => {
  if (showMore.value) {
    textlength.value = true
    checkTextLength()
  }
}
const showShrink = () => {
  if (showText.value) {
    textlength.value = false
    checkTextLength()
  }
}
</script>

<style lang="scss" scoped>
.card-swiper {
  --wot-swiper-radius: 0;
  --wot-swiper-item-padding: 0 24rpx;
  --wot-swiper-nav-dot-color: #e7e7e7;
  --wot-swiper-nav-dot-active-color: #4d80f0;
  padding-bottom: 24rpx;

  :deep(.custom-indicator-class) {
    bottom: -16px;
  }

  :deep(.custom-image) {
    border-radius: 20rpx;
  }
}

.view-detail {
  color: #fff;
}

.conpany-img {
  width: 100%;
  min-height: 100vh;
  background-image: url('/static/img/Mask_group(25).png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}

.uScrollList-scroll-view {
  white-space: nowrap;
}

:deep(.u-read-more__toggle) {
  background-image: none !important;
}

.company-list {
  padding: 40rpx;
  margin-top: 20rpx;

  .company-logos {
    width: 128rpx !important;
    height: 128rpx !important;
    border-radius: 18rpx;
  }

  .company-title {
    flex: 1;
    margin-left: 20rpx;
    color: #fff;

    .company-name {
      font-size: 32rpx;
      font-weight: 500;
      color: #f4bb86;
    }

    .company-num {
      position: relative;
      padding-left: 10rpx;
    }

    .company-num::after {
      position: absolute;
      top: 7rpx;
      left: 0rpx;
      width: 1rpx;
      height: 20rpx;
      content: '';
      background-color: #fff;
    }
  }

  .company-js {
    padding: 38rpx 0 16rpx 0;
  }

  .transparent-tabs {
    top: 20rpx;
    min-height: 500rpx;
    color: #fff;
    background-color: transparent !important; /* 整体背景透明 */

    :deep(.wd-tabs__nav-container) {
      right: 2%; /* 将标题容器靠左对齐 */
    }

    :deep(.wd-tabs__line) {
      // width: 110rpx !important;
      background-color: transparent !important;
    }
    /* 标签页导航项字体大小控制 */
    :deep(.wd-tabs__nav-item) {
      display: flex !important;
      align-items: flex-end !important; /* 垂直对齐到底部 */
      justify-content: center !important;
      padding: 10rpx;
      font-size: 28rpx !important; /* 未选中时的字体大小 */
      transition: font-size 0.3s ease;
    }

    :deep(.wd-tabs__nav-item.is-active) {
      // font-weight: 500;
      display: flex !important;
      align-items: flex-end !important; /* 垂直对齐到底部 */
      justify-content: center !important;
      font-size: 32rpx !important; /* 选中时的字体大小 */
    }

    :deep(.wd-tabs__container) {
      margin: 20rpx 0 30rpx 0;
    }
  }

  .transparent-tabs ::v-deep .wd-tabs__nav {
    background-color: transparent !important; /* 标签栏背景透明 */
  }

  .transparent-tabs ::v-deep .wd-tab__panel {
    background-color: transparent !important; /* 内容区域背景透明 */
  }
  /* 备用选择器 */
  .transparent-tabs ::v-deep .wd-tabs__nav-item {
    display: flex !important;
    align-items: flex-end !important; /* 垂直对齐到底部 */
    justify-content: center !important;
    font-size: 28rpx !important;
    color: #8b8888;
  }

  .transparent-tabs ::v-deep .wd-tabs__nav-item.is-active {
    display: flex !important;
    align-items: flex-end !important; /* 垂直对齐到底部 */
    justify-content: center !important;
    font-size: 32rpx !important;
  }

  .company-adress {
    padding: 54rpx 0 0 0;

    .company-adress-img {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 120rpx;
      height: 60rpx;
      margin-left: 20rpx;
      line-height: 60rpx;
      border: 1rpx solid #f0bb80;
      border-radius: 20rpx;

      .company-adress-icon {
        width: 36rpx;
        height: 36rpx;
        line-height: 60rpx;
        background-image: url('/static/img/daohang_1.png');
        background-position: 100% 100%;
        background-size: 100% 100%;
      }
    }
  }
}

.company-benefit {
  .benefit-list {
    .benefit-list-item {
      display: inline-block;
      padding: 30rpx 40rpx;
      margin-right: 30rpx !important;
      background-color: #2b2b2b;
      border-radius: 20rpx;
    }

    .benefit-list-name {
      color: #888;
    }

    .benefit-list-img {
      width: 32rpx !important;
      height: 32rpx !important;
    }
  }
  /* 新增：网格布局样式 - 一行三个框 */
  .benefit-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;

    .benefit-grid-item {
      box-sizing: border-box;
      width: calc(33.333% - 13.333rpx); /* 一行三个，减去间距 */
      padding: 30rpx 8rpx;
      background-color: #2b2b2b;
      border-radius: 20rpx;

      .benefit-item-content {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
      }

      .benefit-list-name {
        margin-left: 10rpx;
        font-size: 28rpx;
        color: #888;
      }

      .benefit-list-img {
        width: 32rpx !important;
        height: 32rpx !important;
      }
    }
  }
  /* 展开/收起按钮样式 */
  .benefit-toggle-btn {
    display: flex;
    align-items: flex-end; /* 改为底部对齐 */
    justify-content: center;
    padding: 20rpx;
    // margin-top: 20rpx;
    margin-right: 40rpx;
    cursor: pointer;
    // background-color: #2b2b2b;
    border-radius: 20rpx;
    // transition: background-color 0.3s ease;

    &:hover {
      // background-color: #3a3a3a;
    }

    .toggle-text {
      margin-right: 10rpx;
      font-size: 28rpx;
      line-height: 24rpx; /* 设置行高与图标高度一致 */
      color: #f4bb86;
    }

    .toggle-icon-img {
      display: block; /* 确保图标为块级元素 */
      width: 24rpx;
      height: 24rpx;
      margin-right: 10rpx;
      transition: transform 0.3s ease;
    }
  }
}
/* 更多岗位样式 */
.job-container {
  padding-bottom: 20rpx;
}

.job-list {
  display: grid;
  grid-template-rows: auto auto;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  padding-right: 40rpx;
}

.job-card {
  box-sizing: border-box;
  width: calc(50% - 10rpx);
  min-width: 360rpx;
  max-width: 500rpx;
  padding: 20rpx 16rpx;
  cursor: pointer;
  background-color: #2b2b2b;
  border-radius: 20rpx;
  transition: background-color 0.3s ease;

  &:active {
    background-color: #3a3a3a;
  }
}

.job-card-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.job-info-row {
  display: flex;
  align-items: center;
}

.job-icon {
  width: 28rpx !important;
  height: 28rpx !important;
  margin-right: 16rpx;
}

.job-title {
  flex: 1;
  overflow: hidden;
  font-weight: 500;
  line-height: 1.4;
  color: #b6b6b6;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.job-salary {
  flex: 1;
  font-weight: 400;
  color: #f4bb86;
}
/* 查看全部按钮样式 */
.view-all-btn {
  display: flex;
  align-items: center;

  &:active {
    opacity: 0.7;
  }
}

.view-all-text {
  margin-right: 8rpx;
  font-size: 24rpx;
  color: #b6b6b6;
}

.view-all-arrow {
  margin-top: 5rpx;
  font-size: 20rpx;
  font-weight: bold;
  color: #b6b6b6;
}

.company-list-job {
  margin-top: -20rpx;
}
</style>
