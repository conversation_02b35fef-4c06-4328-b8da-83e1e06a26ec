import type { UploadMethod } from 'wot-design-uni/components/wd-upload/types'
import { uploadImgThrum } from '@/service/attachment'

/** 上传hooks */
export const useUpload = () => {
  const uploadMethod: UploadMethod = async (file, formData, options) => {
    const { url } = file
    const onSuccess = (data: string) => {
      options.onSuccess(
        {
          errMsg: 'uploadFile:ok',
          data,
          statusCode: options.statusCode,
        },
        file,
        formData,
      )
    }
    const onError = (error: string) => {
      options.onError({ errMsg: error }, file, formData)
    }
    try {
      await uploadImgThrum({
        filePath: url,
        name: options.name,
        getTask(task) {
          task.onProgressUpdate((res) => {
            options.onProgress(res, file)
          })
        },
        custom: {
          catch: true,
        },
      })
        .then(async ({ data }) => {
          onSuccess(JSON.stringify(data))
        })
        .catch((err) => {
          console.log('err', err)
          onError('上传失败')
        })
    } catch (error) {
      onError('上传失败')
    }
  }
  return {
    uploadMethod,
  }
}
