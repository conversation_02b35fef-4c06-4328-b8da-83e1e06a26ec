<template>
  <view class="page_list">
    <view
      v-for="(listItem, listIndex) in jobList"
      :key="`job-item-${listItem.id}-${listIndex}`"
      class="page_flex"
    >
      <view class="page_flex_colom" @click="goDetail">
        <view class="page_flex_list">
          <view class="flex-c">
            <wd-img v-if="listItem.isRecruit === 1" :height="16" :src="jz" :width="33" />
            <view class="page_left">
              {{
                listItem.positionMarkName
                  ? truncateText(listItem.positionMarkName, 8)
                  : truncateText(listItem.positionName, 8)
              }}
            </view>
          </view>

          <view class="page_right salary">
            <text>{{ listItem.workSalaryBegin }}</text>
            <text v-if="listItem.workSalaryEnd">-</text>
            <text v-if="listItem.workSalaryEnd">{{ listItem.workSalaryEnd }}/月</text>
          </view>
        </view>
        <view class="page_flex_list">
          <view class="flex items-center">
            <view class="page_left_1">{{ truncateText(listItem.companyName, 14) }}</view>
            <view v-if="listItem.sizeName" class="page_left_1">·</view>
            <view v-if="listItem.sizeName" class="page_left_1">
              {{ listItem.sizeName }}
            </view>
          </view>
          <view class="page_right_flex">
            <!-- <wd-icon name="location" size="14px" color="#999"></wd-icon> -->
            <!-- <wd-img :width="10" :height="10" :src="location" /> -->
            <view class="page_right_distance">
              {{ listItem.distanceMeters ? listItem.distanceMeters : listItem.districtName }}
            </view>
          </view>
        </view>

        <view class="bg_end m-b-10rpx">
          <view class="bg_left relative">
            <!--  -->
            <view :class="listItem.isOnline ? 'border-twinkle bg_left_icon_box' : ''">
              <image :src="listItem.hrPositionUrl" class="bg_left_icon" mode="aspectFill"></image>
            </view>

            <view class="bg_left_flex">
              <view class="bg_left_name flex items-center">
                <view class="flex items-center">
                  <view class="c-#333 text-24rpx">{{ listItem.hrUserName }}</view>
                </view>
                <view v-if="listItem.hrPosition" class="m-l-5rpx m-r-5rpx">·</view>
                <view class="c-#333 text-24rpx">
                  {{ truncateText(listItem.hrPosition, 5) }}
                </view>
              </view>
              <view
                :class="listItem.activityStatus === 1 ? 'c-#0ea500' : 'c-#333333'"
                class="bg_left_date"
              >
                {{ listItem.activityStatusLabel }}
              </view>
            </view>
          </view>
          <view class="flex-c">
            <view class="bg_right m-r-20rpx" @click.stop="goJob">
              <image
                class="bg_right_icon-1"
                mode="aspectFill"
                src="/static/img/td-job-card.png"
              ></image>
            </view>
            <view class="bg_right" @click.stop="goChat">
              <image
                class="bg_right_icon"
                mode="aspectFill"
                src="/static/images/home/<USER>"
              ></image>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
  <wd-message-box />
</template>

<script lang="ts" setup>
import { truncateText } from '@/utils/util'
import jz from '@/static/img/home/<USER>'
import pk from '@/static/img/home/<USER>'
import resumeMatching from '@/static/common/resume-matching.png'
import location from '@/static/img/location.png'
import { DICT_IDS } from '@/enum/diction'
import { ref, computed, watch, onMounted } from 'vue'
import { useMessage } from 'wot-design-uni'

const message = useMessage()

const props = defineProps({
  jobList: {
    type: Array<any>,
    default: () => [],
  },
})
// 去沟通item
const goChat = async () => {
  message
    .confirm({
      msg: '同意隐私政策后您可体验该服务，我们将妥善保护您的隐私。',
      title: '提示',
      confirmButtonText: '去授权',
    })
    .then(() => {
      uni.reLaunch({
        url: '/pages/login/index',
      })
      uni.$emit('agreePrivacy')
      console.log('点击了确定按钮')
    })
    .catch(() => {
      console.log('点击了取消按钮')
    })
}

const goDetail = () => {
  message
    .confirm({
      msg: '同意隐私政策后您可体验该服务，我们将妥善保护您的隐私。',
      title: '提示',
      confirmButtonText: '去授权',
    })
    .then(() => {
      uni.reLaunch({
        url: '/pages/login/index',
      })
      uni.$emit('agreePrivacy')
      console.log('点击了确定按钮')
    })
    .catch(() => {
      console.log('点击了取消按钮')
    })
}

const goJob = async () => {
  message
    .confirm({
      msg: '同意隐私政策后您可体验该服务，我们将妥善保护您的隐私。',
      title: '提示',
      confirmButtonText: '去授权',
    })
    .then(() => {
      uni.reLaunch({
        url: '/pages/login/index',
      })
      console.log('点击了确定按钮')
      uni.$emit('agreePrivacy')
    })
    .catch(() => {
      console.log('点击了取消按钮')
    })
}
</script>

<style lang="scss" scoped>
.page_left_1 {
  font-size: 24rpx !important;
  line-height: 44rpx;
  color: #333333;
}

.bg_left_icon_box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 65rpx;
  height: 65rpx;
  border: 3rpx solid #0ea500;
  border-radius: 50rpx;
}

.bg_end {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-top: 6rpx;

  .bg_right {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 140rpx;
    height: 64rpx;
    text-align: center;
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    box-shadow: 4px 4px 16.5px 0px rgba(0, 0, 0, 0.1);

    &_icon {
      width: 50rpx;
      height: 50rpx;
    }

    &_icon-1 {
      width: 50rpx;
      height: 50rpx;
    }
  }

  .bg_left {
    display: flex;
    flex-direction: row;
    align-items: center;

    .bg_left_icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50rpx;
    }

    .bg_left_flex {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding-left: 15rpx;

      .bg_left_name {
        font-size: 22rpx;
        line-height: 44rpx;
        color: #555555;
      }

      .bg_left_date {
        font-size: 22rpx;
      }
    }
  }
}

.bg_flex {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  width: 100%;
  padding-top: 5rpx !important;
  padding-bottom: 14rpx;

  .bg_box {
    padding: 5rpx 20rpx;
    margin-right: 15rpx;
    font-size: 22rpx;
    font-weight: 400;
    color: #333333;
    background: #f3f3f3;
    border-radius: 6rpx 6rpx 6rpx 6rpx;
  }
}

.page_list {
  box-sizing: border-box;
  width: 100%;
  padding: 0 30rpx;
  margin-bottom: 60rpx;

  .page_flex {
    width: 100%;
    padding: 20rpx 30rpx;
    margin-bottom: 20rpx;
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

    .page_flex_colom {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      width: 100%;

      .page_flex_list {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .page_right_flex {
          display: flex;
          flex-direction: row;
          align-items: center;
          padding: 5rpx 0;

          .page_left_1 {
            font-size: 24rpx !important;
            font-weight: 400;
            line-height: 44rpx;
            color: #666;
          }

          .page_right_distance {
            font-size: 22rpx;
            font-weight: 400;
            line-height: 44rpx;
            color: #333333;
          }
        }

        .page_left {
          font-size: 38rpx;
          font-weight: 500;
          line-height: 44rpx;
          color: #333333;
        }

        .page_right {
          font-size: 28rpx;
          font-weight: 500;
          line-height: 44rpx;
          color: #888888;
        }
      }
    }
  }
}

.border-twinkle {
  position: relative;

  &::before {
    position: absolute;
    top: -2rpx;
    right: -2rpx;
    bottom: -2rpx;
    left: -2rpx;
    z-index: -1;
    content: '';
    background: linear-gradient(45deg, #0ea500, #00ff00, #0ea500);
    border-radius: 50rpx;
    animation: twinkle 2s infinite;
  }
}

@keyframes twinkle {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.stateType {
  padding: 2rpx 8rpx;
  margin-left: 8rpx;
  font-size: 20rpx;
  color: #ff5151;
  background: rgba(255, 81, 81, 0.1);
  border-radius: 4rpx;
}

.border-top {
  border-top: 2rpx dashed #d8d8d8;
}

.salary {
  color: #ff8080 !important;
}
</style>
