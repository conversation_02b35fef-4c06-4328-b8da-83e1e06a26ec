import { POSTPaging, POST } from '../index'
import { hrUnInterestUserAddDataInt, hrUnInterestUserCancelDataInt } from './types'
import { HttpRequestConfig } from 'luch-request'

/** hr添加不感兴趣的人接口 */
export const hrUnInterestUserAdd = (data: hrUnInterestUserAddDataInt, config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/hrUnInterestUser/add', data, config)

/** hr取消不感兴趣的人接口 */
export const hrUnInterestUserCancel = (
  data: hrUnInterestUserCancelDataInt,
  config?: HttpRequestConfig,
) => POST('/easyzhipin-api/hrUnInterestUser/cancel', data, config)
