import { POST } from '@/service'
// 手机号
export const updatePhone = (data) => {
  return POST('/easyzhipin-api/resume/updatePhone', data)
}
// 微信
export const updateWxCode = (data) => {
  return POST('/easyzhipin-api/resume/updateWxCode', data)
}
// 获取手机号
export const queryPhone = (data) => {
  return POST('/easyzhipin-api/resume/queryPhone', data)
}
// 获取微信
export const queryWxCode = () => {
  return POST('/easyzhipin-api/resume/queryWxCode')
}
// 首次工作时间
export const updateWorkTime = (data) => {
  return POST('/easyzhipin-api/resume/updateWorkTime', data)
}
// 地址
export const myAddress = (data) => {
  return POST('/easyzhipin-api/my/myAddress', data)
}
// 保存

export const saveMyAddress = (data) => {
  return POST('/easyzhipin-api/my/saveMyAddress', data)
}
// 头像上传
export const userImageAudit = (data) => {
  return POST('/easyzhipin-api/userImageAudit/add', data)
}
// 收藏公司列表
export const queryCollectCompanyList = (data) => {
  return POST('/easyzhipin-api/myDetails/queryCollectCompanyList', data)
}
// 收藏岗位列表
export const queryCollectPositionList = (data) => {
  return POST('/easyzhipin-api/myDetails/queryCollectPositionList', data)
}

// 根据状态查看我发布的岗位
export const queryMyPositionListByStatus = (data) => {
  return POST('/easyzhipin-api/hrPosition/queryMyPublishList', data)
}

// 查询我的优惠卷
export const queryMyCoupons = (data) => {
  return POST('/easyzhipin-api/payUserCoupon/queryList', data)
}

// 登录设备管理
export const queryLoginDeviceList = () => {
  return POST('/easyzhipin-api/login/myPhoneDevice', {})
}

// 获取旧手机号码的验证码
export const sendOldPhoneCode = () => {
  return POST('/easyzhipin-api/login/sendSmsMyPhone', {})
}

// 获取新手机号码的验证码
export const sendUpdatePhoneCode = (data) => {
  return POST('/easyzhipin-api/login/sendSmsPhone', data)
}

// 更新手机号码
export const updateMyPhone = (data) => {
  return POST('/easyzhipin-api/login/updateSmsPhone', data)
}

// 发送更新密码的验证码
export const sendUpdatePasswordCode = () => {
  return POST('/easyzhipin-api/login/sendSmsCode', {})
}

// 更新密码
export const updateMyPassword = (data) => {
  return POST('/easyzhipin-api/login/updatePassword', data)
}
