import { CommonUtil } from 'wot-design-uni'
import pLimit from 'p-limit'
import { chat<PERSON><PERSON> } from '@/ChatUIKit/sdk'
import { EasemobChat } from 'easemob-websdk'
import { currRoute } from '@/utils'
import { IM_LISTENER_TYPE, USER_TYPE, PUSH_NOTIFICATION_TYPE, CONVERSATION_MARKS } from '@/enum'
import { imSessionRecordSaveSession } from '@/service/imSessionRecord'
import { resumeQueryMyFileResumeList } from '@/service/resume'
import {
  exchangeResumeRecordSendResume,
  exchangeResumeRecordBatchSendResume,
  exchangeResumeRecordBatchSendResumeCallback,
} from '@/service/exchangeResumeRecord'
import { exchangeNumberExchange } from '@/service/exchangeNumber'
import { exchangeCodeExchange } from '@/service/exchangeCode'
import { sysUserCallQueryOne } from '@/service/sysUserCall'
import { hrExchangeResumeRecordRequestResume } from '@/service/hrExchangeResumeRecord'
import { hrExchangeNumberExchange } from '@/service/hrExchangeNumber'
import { hrExchangeCodeExchange } from '@/service/hrExchangeCode'
import { imUnInterestUserNameList } from '@/service/im'
import type { UserInfoWithPresence, UIKITConversationItem } from '@/ChatUIKit/types'
import type { positionInfoQueryIMCardInfoByIdInt } from '@/service/positionInfo/types'
import type { exchangeCodeExchangeDataInt } from '@/service/exchangeCode/types'
import type { hrResumeQueryIMCardInfoByIdInt } from '@/service/hrResume/types'
import type {
  batchSendResumeDataInt,
  BatchSendMessageListInt,
} from '@/service/exchangeResumeRecord/types'

interface PayloadMsg {
  type: PUSH_NOTIFICATION_TYPE
  from: string
  chatType: string
}

interface SendResult {
  recipient: string
  status: 'fulfilled' | 'rejected'
  reason?: any
}

type ConversationMarkType = (typeof CONVERSATION_MARKS)[keyof typeof CONVERSATION_MARKS]
export type ConversationWithUserInfo = UIKITConversationItem &
  UserInfoWithPresence &
  EasemobChat.ConversationItem

interface CustomMessage
  extends Omit<EasemobChat.CreateCustomMsgParameters, 'type' | 'chatType' | 'customExts'> {
  customExts: Api.IM.CustomMessage.ExtInfo
}

interface SendMassResumeMessageParams extends Pick<batchSendResumeDataInt, 'greeting'> {
  sendList: BatchSendMessageListInt[]
}
const FIXED_BLACKLIST_CONFIG = {
  users: ['easyzhipin_push_admin'],
  enabled: true, // 可以控制是否启用固定黑名单
} as const
const FIXED_BLACKLIST_USERS = FIXED_BLACKLIST_CONFIG.enabled ? FIXED_BLACKLIST_CONFIG.users : []
const MESSAGE_STATUS = {
  DELIVERED: '送达',
  READ: '已读',
  FAILED: '失败',
} as const
const LOAD_CONVERSATIONS_CONFIG = {
  DEBOUNCE_TIME: 300,
  CACHE_DURATION: 5000,
  BATCH_SIZE: 10,
} as const
const HIGH_PRIORITY_MARKS = [
  CONVERSATION_MARKS.EXCHANGED as ConversationMarkType,
  CONVERSATION_MARKS.INTERVIEWED as ConversationMarkType,
] as const
const chatType: EasemobChat.ChatType = 'singleChat'
const conversationMarkQueues = new Map<string, ReturnType<typeof pLimit>>()
const customCardInfo = ref<positionInfoQueryIMCardInfoByIdInt>({})
const customBusinessCardInfo = ref<hrResumeQueryIMCardInfoByIdInt>({})
const conversationList = ref<ConversationWithUserInfo[]>([])
const unInterestBlackUserNameList = ref<string[]>([])
const imUsersBlocklist = ref<string[]>([])
const totalUnreadCount = ref(0)

export const useIMConversation = () => {
  const { sm4Encrypt } = useSmCrypto({
    type: 'password',
  })
  const { userIntel, userRoleIsBusiness } = useUserInfo()
  const lastUpdatedTime = ref(0)
  /**
   * 加载会话列表
   * @param forceRefresh 是否强制刷新
   */
  const loadConversations = CommonUtil.debounce(async (forceRefresh: boolean = false) => {
    if (!uni.$UIKit) return
    const now = Date.now()
    if (!forceRefresh && now - lastUpdatedTime.value < LOAD_CONVERSATIONS_CONFIG.CACHE_DURATION) {
      return
    }
    try {
      const [, blacklist] = await Promise.all([
        uni.$UIKit.convStore.getConversationList(),
        updateBlacklistUsers(forceRefresh),
        updateImUsersBlocklist(forceRefresh),
      ])

      const conversationStoreList = uni.$UIKit.convStore.conversationList
      const filteredConversations = conversationStoreList.filter(
        (item) => !blacklist.includes(item.conversationId),
      )
      if (filteredConversations.length === 0) {
        conversationList.value = []
        totalUnreadCount.value = 0
        lastUpdatedTime.value = now
        return
      }
      const userIdList = filteredConversations.map((item) => item.conversationId)
      const userInfoPromise = uni.$UIKit.appUserStore.getUsersInfoFromServer({ userIdList })
      const presencePromise = uni.$UIKit.appUserStore
        .getUsersPresenceFromServer({ userIdList })
        .catch(() => {
          return null
        })
      await Promise.allSettled([userInfoPromise, presencePromise])
      await batchUpdateOfflineMessageMarks(filteredConversations)
      conversationList.value = enrichConversationsWithUserInfo(filteredConversations)
      totalUnreadCount.value = calculateUnreadCount(conversationList.value)
      updateAppBadge(totalUnreadCount.value)
      lastUpdatedTime.value = now
      console.log('conversationList loaded:', conversationList.value.length)
    } catch (error) {
      console.error('加载会话列表失败:', error)
    }
  }, LOAD_CONVERSATIONS_CONFIG.DEBOUNCE_TIME)
  /**
   * 从本地会话列表中获取单条会话（不触发网络请求）
   * @param conversationId 会话ID
   * @returns ConversationWithUserInfo | undefined
   */
  const getLocalConversationById = (
    conversationId: string,
  ): ConversationWithUserInfo | undefined => {
    return conversationList.value.find((item) => item.conversationId === conversationId)
  }
  /**
   * 统一更新黑名单用户列表
   * @param forceRefresh 是否强制刷新
   * @returns Promise<string[]> 返回最新的黑名单列表
   */
  const updateBlacklistUsers = async (forceRefresh: boolean = false): Promise<string[]> => {
    try {
      if (!forceRefresh && unInterestBlackUserNameList.value.length > 0) {
        return unInterestBlackUserNameList.value
      }
      const { data: unInterestUserNameList } = await imUnInterestUserNameList()
      const newBlacklist = [...FIXED_BLACKLIST_USERS, ...unInterestUserNameList]
      unInterestBlackUserNameList.value = newBlacklist
      return newBlacklist
    } catch (error) {
      const fallbackBlacklist = [...FIXED_BLACKLIST_USERS]
      unInterestBlackUserNameList.value = fallbackBlacklist
      return fallbackBlacklist
    }
  }
  /**
   * 统一更新环信黑名单用户列表
   * @param forceRefresh 是否强制刷新
   * @returns Promise<string[]> 返回最新的黑名单列表
   */
  const updateImUsersBlocklist = async (forceRefresh: boolean = false): Promise<string[]> => {
    try {
      if (!forceRefresh && imUsersBlocklist.value.length > 0) {
        return imUsersBlocklist.value
      }
      const { data } = await uni.$UIKit.getChatConn().getBlocklist()
      imUsersBlocklist.value = data
      return data
    } catch (error) {
      return imUsersBlocklist.value
    }
  }
  /** 添加或移除环信黑名单列表 */
  const toggleImUserBlockList = async (name: string[], add: boolean) => {
    try {
      if (add) {
        await uni.$UIKit.getChatConn().addUsersToBlocklist({
          name,
        })
      } else {
        await uni.$UIKit.getChatConn().removeUserFromBlocklist({
          name,
        })
      }
      // await updateImUsersBlocklist(true)
    } catch (error) {
      return Promise.reject(error)
    }
  }
  /** 查看用户是否在环信黑名单里面 */
  const isUserInImBlockList = (userId: string) => {
    return imUsersBlocklist.value.includes(userId)
  }
  const handleConversationMarkForMessage = async (from: string) => {
    try {
      await smartUpdateConversationMark(from, CONVERSATION_MARKS.NEW_GREETING)
      console.log(`为会话 ${from} 智能设置 NEW_GREETING 标记`)
      loadConversations(true)
    } catch (error) {
      console.error(`智能设置消息标记失败 ${from}:`, error)
    }
  }
  /** 监听新消息以更新未读计数 */
  const listenForNewMessages = () => {
    if (!uni.$UIKit) return
    uni.$UIKit.getChatConn().addEventHandler(IM_LISTENER_TYPE.RECEIVE_MESSAGE, {
      onTextMessage: handleTextMessage,
      onImageMessage: async (msg) => {
        await handleConversationMarkForMessage(msg.from)
      },
      onCustomMessage: handleCustomMessage,
      onReceivedMessage: () => {
        loadConversations(true)
      },
      onReadMessage: async (msg) => {
        console.log('收到已读回执', msg)
      },
      onDeliveredMessage: async (msg) => {
        console.log('收到已送达回执', msg)
      },
    })
  }
  /** 处理文本消息 */
  const handleTextMessage = async (msg: EasemobChat.TextMsgBody) => {
    const { from, chatType, ext } = msg
    const nickname = ext?.ease_chat_uikit_user_info?.nickname ?? ''
    await Promise.allSettled([
      createLocalNotification(
        {
          title: nickname,
          content: getLastTypeMessage(msg),
          payload: { type: PUSH_NOTIFICATION_TYPE.IM_MESSAGE, from, chatType } as PayloadMsg,
        },
        from,
      ),
      handleConversationMarkForMessage(from),
    ])
  }
  /** 处理自定义消息 */
  const handleCustomMessage = async (msg: EasemobChat.CustomMsgBody) => {
    const { customExts, from } = msg
    if (customExts?.type === 'conversation_mark_sync') {
      return handleMarkSyncMessage(msg)
    }
    await handleConversationMarkForMessage(from)
  }
  /** 处理会话标记 */
  const handleMarkSyncMessage = async (msg: EasemobChat.CustomMsgBody) => {
    try {
      const { mark } = msg.customExts as Api.IM.CustomMessage.ExtInfo
      await setConversationMark(msg.from, 'singleChat', mark, false)
      console.log(`收到对方标记同步，会话 ${msg.from} 标记更新为: ${mark}`)
      await updateSingleConversation(msg.from)
    } catch (error) {
      console.error('处理标记同步消息失败:', error)
    }
  }
  /** 单个会话更新 */
  const updateSingleConversation = async (conversationId: string) => {
    try {
      const conversation = uni.$UIKit.convStore.getConversationById(conversationId)
      if (conversation) {
        const userInfo = uni.$UIKit.appUserStore.getUserInfoFromStore(conversationId)
        const updatedConversation = { ...conversation, ...userInfo }
        const index = conversationList.value.findIndex((c) => c.conversationId === conversationId)
        if (index !== -1) {
          conversationList.value[index] = updatedConversation
        }
        console.log('更新单个会话成功:', updatedConversation)
      }
    } catch (error) {
      console.error('更新单个会话失败:', error)
    }
  }
  /** 创建本地通知栏消息 */
  const createLocalNotification = (
    options: UniNamespace.CreatePushMessageOptions,
    from: string,
  ) => {
    // #ifdef APP-PLUS
    if (!unInterestBlackUserNameList.value.includes(from)) {
      uni.createPushMessage({
        ...options,
      })
    }
    // #endif
  }
  /** 监听推送事件 */
  const listenForPushNotifications = () => {
    const pushMsgCallback = (message: UniNamespace.OnPushMessageCallbackOptions) => {
      const { path } = currRoute()
      const { type, data } = message
      const { payload } = data
      if (type === 'click') {
        const chatUrl = '/ChatUIKit/modules/Chat/index'
        const isChat = path === chatUrl
        const { from, chatType } = payload as PayloadMsg
        if (from && chatType) {
          uni[isChat ? 'redirectTo' : 'navigateTo']({
            url: CommonUtil.buildUrlWithParams(chatUrl, {
              type: chatType,
              id: from,
            }),
          })
        }
      }
    }
    // #ifdef APP-PLUS
    uni.onPushMessage(pushMsgCallback)
    // #endif
  }
  /** 关闭推送事件监听 */
  const closePushNotifications = () => {
    // #ifdef APP-PLUS
    uni.offPushMessage()
    // #endif
  }
  /** 获取im用户配置信息 */
  const getIMUserInfo = (userId: string) => {
    return uni.$UIKit.appUserStore.getUserInfoFromStore(userId)
  }
  /** 获取自己的im信息 */
  const getIMUserInfoSelf = computed(() => uni.$UIKit.appUserStore.getSelfUserInfo())
  /** 获取当前会话信息 */
  const getConversationInfo = computed(() => uni.$UIKit.convStore.currConversation)
  /** 获取当前im登录实例 */
  const getIMLoginId = computed<string>(() => uni.$UIKit.getChatConn().user ?? '')
  const getMessageStatusPrefix = (message: EasemobChat.MessageBody) => {
    if (message.from !== getIMLoginId.value) return ''
    return MESSAGE_STATUS.DELIVERED
  }
  const hasConversation = (conversationId: string) => {
    return !!uni.$UIKit.convStore.getConversationById(conversationId)
  }
  /** 根据类型获取最后一条消息 */
  const getLastTypeMessage = (last: EasemobChat.MessageBody) => {
    const messageHandlers = {
      txt: (msg: EasemobChat.TextMsgBody) => msg.msg || '',
      img: () => '[图片]',
      custom: handleCustomMessageContent,
      _default: (msg: any) => `[${msg.type || '未知'}消息]`,
    }
    const handler = messageHandlers[last.type] || messageHandlers._default
    const messageContent = handler(last)
    const statusPrefix = getMessageStatusPrefix(last)
    return statusPrefix ? `「${statusPrefix}」${messageContent}` : messageContent
  }
  /** 自定义消息处理 */
  const handleCustomMessageContent = (msg: EasemobChat.CustomMsgBody): string => {
    const customExts = msg.customExts || {}
    console.log('customExts', customExts)
    if (customExts.type === 'conversation_mark_sync') {
      return handleMarkSyncMessageContent(msg, customExts)
    }
    const customTypeHandlers: Record<string, string> = {
      resume: '[简历]',
      exchange_phone: '[手机号]',
      exchange_wechat: '[微信号]',
      interview_appointment: '[面试邀约]',
    }
    return customTypeHandlers[customExts.type] || '自定义消息'
  }
  /** 自定义同步消息处理 */
  const handleMarkSyncMessageContent = (
    msg: EasemobChat.CustomMsgBody,
    customExts: AnyObject,
  ): string => {
    console.log('处理同步消息内容', customExts)
    const { sourceMessageType } = customExts as Api.IM.CustomMessage.ExtInfo
    if (!sourceMessageType) return '同步消息'
    const isMyOperation = msg.from === getIMLoginId.value
    return getSourceMessageText(sourceMessageType, isMyOperation)
  }
  /** 获取自定义同步消息文本提示 */
  const getSourceMessageText = (
    messageType: Api.IM.CustomMessage.ExtType,
    isMyOperation: boolean,
  ): string => {
    const messageTypeHandlers = {
      resume: () => {
        if (isMyOperation) {
          return userRoleIsBusiness.value ? '您已同意对方的简历请求' : '您已发送简历'
        } else {
          return userRoleIsBusiness.value ? '对方同意发送简历' : '对方同意了您的简历请求'
        }
      },
      exchange_phone: () => (isMyOperation ? '您同意了交换电话' : '对方同意了交换电话'),
      exchange_wechat: () => (isMyOperation ? '您同意了交换微信' : '对方同意了交换微信'),
      interview_appointment: () => (isMyOperation ? '您同意了面试邀约' : '对方同意了面试邀约'),
    }
    const handler = messageTypeHandlers[messageType]
    return handler ? handler() : isMyOperation ? '您已操作' : '对方已操作'
  }
  /** 发送会话标记同步消息 */
  const sendConversationMarkSyncMessage = async (
    to: string,
    mark: ConversationMarkType | number,
    sourceMessageType?: Api.IM.CustomMessage.ExtType,
  ) => {
    try {
      await sendCustomMessage({
        to,
        customEvent: '',
        customExts: {
          type: 'conversation_mark_sync',
          status: 1,
          mark,
          syncTime: Date.now(),
          sourceMessageType,
        },
      })
      console.log(`已发送标记同步消息到 ${to}，标记: ${mark}`)
    } catch (error) {
      console.error('发送标记同步消息失败', error)
    }
  }
  /** 设置会话标记 */
  const setConversationMark = async (
    conversationId: string,
    conversationType: 'singleChat' | 'groupChat',
    mark: ConversationMarkType | number,
    syncToRemote: boolean = false,
    sourceMessageType?: Api.IM.CustomMessage.ExtType,
  ) => {
    const queue = getMarkQueue(conversationId)
    return queue(() =>
      setConversationMarkInternal(
        conversationId,
        conversationType,
        mark,
        syncToRemote,
        sourceMessageType,
      ),
    )
  }
  /**
   * 获取或创建会话标记操作队列
   * @param conversationId 会话ID
   */
  const getMarkQueue = (conversationId: string) => {
    if (!conversationMarkQueues.has(conversationId)) {
      conversationMarkQueues.set(conversationId, pLimit(1))
    }
    return conversationMarkQueues.get(conversationId)!
  }

  /** 设置会话标记（内部方法，不使用队列，避免嵌套） */
  const setConversationMarkInternal = async (
    conversationId: string,
    conversationType: 'singleChat' | 'groupChat',
    mark: ConversationMarkType | number,
    syncToRemote: boolean = false,
    sourceMessageType?: Api.IM.CustomMessage.ExtType,
  ) => {
    try {
      const conversation = uni.$UIKit.convStore.getConversationById(conversationId)
      const existingMarks = conversation?.marks || []

      const removePromises = existingMarks
        .filter((existingMark) => existingMark !== mark)
        .map((existingMark) =>
          removeConversationMarkInternal(
            conversationId,
            conversationType,
            existingMark as ConversationMarkType,
          ),
        )

      if (removePromises.length > 0) {
        await Promise.allSettled(removePromises)
      }

      if (!existingMarks.includes(mark)) {
        await uni.$UIKit.getChatConn().addConversationMark({
          conversations: [
            {
              conversationId,
              conversationType,
            },
          ],
          mark,
        })
      }
      if (syncToRemote) {
        await sendConversationMarkSyncMessage(conversationId, mark, sourceMessageType)
      }
    } catch (error) {
      console.error('设置会话标记失败', error)
    }
  }

  /** 移除会话标记（内部方法，不使用队列，避免嵌套） */
  const removeConversationMarkInternal = async (
    conversationId: string,
    conversationType: 'singleChat' | 'groupChat',
    mark: ConversationMarkType,
  ) => {
    try {
      await uni.$UIKit.getChatConn().removeConversationMark({
        conversations: [
          {
            conversationId,
            conversationType,
          },
        ],
        mark,
      })
    } catch (error) {
      console.error('移除会话标记失败', error)
    }
  }

  /**
   * 智能更新会话标记（考虑优先级）
   * @param conversationId 会话ID
   * @param targetMark 目标标记
   * @param syncToRemote 是否同步到远程
   */
  const smartUpdateConversationMark = async (
    conversationId: string,
    targetMark: ConversationMarkType,
    syncToRemote: boolean = false,
    sourceMessageType?: Api.IM.CustomMessage.ExtType,
  ) => {
    const queue = getMarkQueue(conversationId)
    return queue(async () => {
      try {
        const conversation = uni.$UIKit.convStore.getConversationById(conversationId)
        const existingMarks = conversation?.marks || []

        const MARK_PRIORITY = {
          [CONVERSATION_MARKS.NEW_GREETING]: 1,
          [CONVERSATION_MARKS.ONLY_CHAT]: 2,
          [CONVERSATION_MARKS.EXCHANGED]: 3,
          [CONVERSATION_MARKS.INTERVIEWED]: 4,
        }

        const targetPriority = MARK_PRIORITY[targetMark]
        const hasHigherPriorityMark = existingMarks.some(
          (mark) => MARK_PRIORITY[mark as ConversationMarkType] > targetPriority,
        )
        if (hasHigherPriorityMark) {
          console.log(`会话 ${conversationId} 已有更高优先级标记，跳过更新到 ${targetMark}`)
          return
        }
        if (existingMarks.includes(targetMark)) {
          console.log(`会话 ${conversationId} 已有标记 ${targetMark}，跳过更新`)
          return
        }
        await setConversationMarkInternal(
          conversationId,
          'singleChat',
          targetMark,
          syncToRemote,
          sourceMessageType,
        )
        console.log(`会话 ${conversationId} 标记已更新为: ${targetMark}`)
      } catch (error) {
        console.error('智能更新会话标记失败:', error)
      }
    })
  }
  /** 移除会话标记 */
  const removeConversationMark = async (
    conversationId: string,
    conversationType: 'singleChat' | 'groupChat',
    mark: ConversationMarkType,
  ) => {
    const queue = getMarkQueue(conversationId)
    return queue(() => removeConversationMarkInternal(conversationId, conversationType, mark))
  }
  /** 清理所有会话的标记队列 */
  const clearMarkQueues = () => {
    conversationMarkQueues.clear()
  }
  /** 清理单个会话的队列（当会话被删除时） */
  const clearConversationMarkQueue = (conversationId: string) => {
    conversationMarkQueues.delete(conversationId)
  }
  /** 处理历史消息中的标记同步 */
  const processHistoryMarkSyncMessages = async () => {
    try {
      const conversations = conversationList.value
      const currentUserId = getIMLoginId.value
      const batchQueue = pLimit(3)
      const tasks = conversations.map((conversation) =>
        batchQueue(async () => {
          const { conversationId } = conversation
          const queue = getMarkQueue(conversationId)

          return queue(async () => {
            try {
              const messages = await uni.$UIKit.getChatConn().getHistoryMessages({
                targetId: conversationId,
                chatType: 'singleChat',
                pageSize: 50,
              })
              if (messages?.messages?.length) {
                const markSyncMessages = messages.messages.filter(
                  (msg) =>
                    msg.type === 'custom' &&
                    msg.customExts?.type === 'conversation_mark_sync' &&
                    msg.from !== currentUserId,
                )
                if (markSyncMessages.length > 0) {
                  const latestSyncMessage = markSyncMessages[
                    markSyncMessages.length - 1
                  ] as EasemobChat.CustomMsgBody
                  const { mark } = latestSyncMessage.customExts as Api.IM.CustomMessage.ExtInfo & {
                    mark: ConversationMarkType
                  }
                  const currentMarks = conversation.marks || []
                  if (!currentMarks.includes(mark)) {
                    await setConversationMarkInternal(conversationId, 'singleChat', mark, false)
                    console.log(`处理历史标记同步消息，会话 ${conversationId} 标记更新为: ${mark}`)
                  }
                }
              }
            } catch (error) {
              console.error(`处理会话 ${conversationId} 历史标记同步消息失败:`, error)
            }
          })
        }),
      )

      await Promise.allSettled(tasks)
    } catch (error) {
      console.error('处理历史标记同步消息失败:', error)
    }
  }
  /** 根据消息类型自动更新会话标记 */
  const autoUpdateConversationMark = async (
    conversationId: string,
    messageType?: Api.IM.CustomMessage.ExtType,
    status?: Api.IM.CustomMessage.StatusType,
    syncToRemote: boolean = true,
  ) => {
    if (!messageType || status !== 1) return
    if (messageType === 'conversation_mark_sync') {
      console.log('标记同步消息应由 handleMarkSyncMessage 处理')
      return
    }
    const messageToMarkMap: Record<
      Exclude<Api.IM.CustomMessage.ExtType, 'conversation_mark_sync'>,
      ConversationMarkType | null
    > = {
      resume: CONVERSATION_MARKS.EXCHANGED,
      exchange_phone: CONVERSATION_MARKS.EXCHANGED,
      exchange_wechat: CONVERSATION_MARKS.EXCHANGED,
      interview_appointment: CONVERSATION_MARKS.INTERVIEWED,
      uninterested: null,
    }

    const targetMark = messageToMarkMap[messageType]
    if (!targetMark) return

    try {
      await smartUpdateConversationMark(conversationId, targetMark, syncToRemote, messageType)
      console.log(
        `会话 ${conversationId} 根据消息类型 ${messageType} 自动更新标记为: ${targetMark}`,
      )
    } catch (error) {
      console.error('自动更新会话标记失败:', error)
    }
  }

  /** 发送即时文本消息 */
  const sendIMTextMessage = async (to: string, msg: string, force: boolean = false) => {
    if (!hasConversation(to) || force) {
      const createMsg = chatSDK.message.create({
        type: 'txt',
        to,
        chatType,
        msg,
      })
      uni.$UIKit.messageStore.sendMessage(createMsg)
      await CommonUtil.pause(500)
      await smartUpdateConversationMark(to, CONVERSATION_MARKS.ONLY_CHAT)
    }
  }

  /** 发送自定义消息 */
  const sendCustomMessage = async (message: CustomMessage) => {
    const { to, ...params } = message
    const createMsg = chatSDK.message.create({
      type: 'custom',
      to,
      chatType,
      ...params,
    })
    uni.$UIKit.messageStore.sendMessage(createMsg)
    await CommonUtil.pause(500)
    await smartUpdateConversationMark(to, CONVERSATION_MARKS.ONLY_CHAT)
  }
  /** 发送邀约面试 */
  const sendInvitationForInterview = async (
    to: string,
    customExts: Api.IM.CustomMessage.ExtInfo,
  ) => {
    const { interview_appointment_record_id: id = 0, interviewTime = '' } = customExts
    if (!id || !interviewTime) {
      return Promise.reject(new Error('缺少面试信息'))
    }
    try {
      await sendCustomMessage({
        to,
        customEvent: '',
        customExts,
      })
      return Promise.resolve(customExts)
    } catch (error) {
      return Promise.reject(new Error('发送邀约面试失败'))
    }
  }
  /** 发送微信号 */
  const sendCustomWeChatCodeMessage = async (to: string, exchange: exchangeCodeExchangeDataInt) => {
    const { wechatCode } = exchange
    const sm4WeChatCode = sm4Encrypt(wechatCode)
    try {
      let exchangeResumeRecordId: number | null = null
      let cUserWechat: string | null = null
      let bUserWechat: string | null = null
      if (!userRoleIsBusiness.value) {
        const { data } = await exchangeCodeExchange(exchange)
        exchangeResumeRecordId = data
        cUserWechat = sm4WeChatCode
      } else {
        const { data } = await hrExchangeCodeExchange(exchange)
        exchangeResumeRecordId = data
        bUserWechat = sm4WeChatCode
      }
      await sendCustomMessage({
        to,
        customEvent: '',
        customExts: {
          type: 'exchange_wechat',
          status: 0,
          exchange_wechat_record_id: exchangeResumeRecordId,
          cUserWechat,
          bUserWechat,
        },
      })
    } catch (error) {
      return Promise.reject(new Error('发送微信号失败'))
    }
  }
  /** 发送手机号 */
  const sendCustomPhoneMessage = async (to: string, userId: number) => {
    const sm4PhoneCode = sm4Encrypt(userIntel.value.phone)
    try {
      let exchangeResumeRecordId: number | null = null
      let cUserPhone: string | null = null
      let bUserPhone: string | null = null
      if (!userRoleIsBusiness.value) {
        const { data } = await exchangeNumberExchange({
          phone: userIntel.value.phone,
          userId,
        })
        exchangeResumeRecordId = data
        cUserPhone = sm4PhoneCode
      } else {
        const { data } = await hrExchangeNumberExchange({
          phone: userIntel.value.phone,
          userId,
        })
        exchangeResumeRecordId = data
        bUserPhone = sm4PhoneCode
      }
      await sendCustomMessage({
        to,
        customEvent: '',
        customExts: {
          type: 'exchange_phone',
          status: 0,
          exchange_phone_record_id: exchangeResumeRecordId,
          cUserPhone,
          bUserPhone,
        },
      })
    } catch (error) {
      return Promise.reject(new Error('发送手机号失败'))
    }
  }
  /** 发送/索要简历 */
  const sendCustomResumeMessage = async (to: string, post: AnyObject) => {
    try {
      const {
        id: positionId,
        hxUserInfoVO: { userId: hrId },
      } = post
      let exchangeResumeRecordId: number | null = null
      let cUserResumeLink: string | null = null
      if (!userRoleIsBusiness.value) {
        const { data } = await resumeQueryMyFileResumeList()
        if (!data || !data.length) {
          return Promise.reject(new Error('请先完善简历信息'))
        }
        const [{ fileId: attachmentId, fileUrl: attachmentUrl }] = data
        const { data: resumeId } = await exchangeResumeRecordSendResume({
          attachmentId,
          hrId,
          positionId,
        })
        exchangeResumeRecordId = resumeId
        cUserResumeLink = sm4Encrypt(attachmentUrl)
      } else {
        const { data } = await hrExchangeResumeRecordRequestResume({
          positionId,
          userId: hrId,
        })
        exchangeResumeRecordId = data
      }
      await sendCustomMessage({
        to,
        customEvent: '',
        customExts: {
          type: 'resume',
          status: 0,
          exchange_resume_record_id: exchangeResumeRecordId,
          cUserResumeLink,
        },
      })
    } catch {
      return Promise.reject(new Error('投递失败'))
    }
  }
  /** 打招呼 */
  const sendGreetingMessage = async (
    to: string,
    post: AnyObject = {},
    chat = true,
    force = false,
  ) => {
    try {
      const {
        id: positionId,
        positionName,
        companyId,
        hxUserInfoVO: { userId },
      } = post
      let greetingMsg: string = null
      const roleConfig = {
        [USER_TYPE.HR]: {
          getMessage: (name?: string) =>
            name
              ? `您好，我们正在诚聘${name}，有兴趣聊聊吗？`
              : 'Hi，您好，我们正在诚聘，期待您的加入！',
          getSessionParams: {
            companyId: userIntel.value.companyId,
            hrUserId: userIntel.value.userId,
            positionId,
            userId,
          },
        },
        [USER_TYPE.APPLICANT]: {
          getMessage: (name?: string) =>
            name
              ? `您好，希望和您沟通下${name}`
              : '您好，我对您发布的岗位很感兴趣，希望能进一步了解！',
          getSessionParams: {
            companyId,
            hrUserId: userId,
            positionId,
            userId: userIntel.value.userId,
          },
        },
      }
      const userType = userIntel.value.type
      const { getMessage, getSessionParams } = roleConfig[userType]
      if (getSessionParams.positionId && getSessionParams.companyId) {
        await imSessionRecordSaveSession(getSessionParams, {
          custom: { catch: true },
        })
        const { data } = await sysUserCallQueryOne()
        greetingMsg = data
      }
      await sendIMTextMessage(to, greetingMsg || getMessage(positionName), force)
      if (chat) {
        const url = CommonUtil.buildUrlWithParams('/ChatUIKit/modules/Chat/index', {
          type: 'singleChat',
          id: to,
        })
        uni.navigateTo({ url })
      }
    } catch (error) {
      console.error('发送打招呼消息失败', error)
      throw error
    }
  }
  /** 发送简历 */
  const sendResumeMessage = async (to: string, post: AnyObject = {}) => {
    const TOAST_DURATION = 2000
    try {
      await sendCustomResumeMessage(to, post)
      await sendGreetingMessage(to, post, false)
      uni.showToast({ title: '投递成功', icon: 'none', duration: TOAST_DURATION })
      return 1
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '投递失败'
      // uni.showToast({ title: errorMessage, icon: 'none', duration: TOAST_DURATION })
      return 0
    }
  }
  /** 群发简历loading管理类 */
  class LoadingManager {
    private timer: ReturnType<typeof setInterval> | null = null
    showProgress(total: number, failed: string[]) {
      const current = total - failed.length
      uni.showLoading({ title: `投递中 ${current}/${total}`, mask: true })
      if (total > 1) {
        this.timer = setInterval(() => {
          const currentCount = total - failed.length
          uni.showLoading({ title: `投递中 ${currentCount}/${total}`, mask: true })
        }, 3000)
      }
    }

    hide() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
      uni.hideLoading()
    }
  }
  /** 发送单条简历消息 */
  const sendSingleResumeMessage = async (
    exchangeResumeRecordId: number,
    hrUserId: number,
    sendList: SendMassResumeMessageParams['sendList'],
    greeting: string,
    cUserResumeLink: string,
  ): Promise<SendResult> => {
    const targetUser = sendList.find((item) => item.hrUserId === hrUserId)
    if (!targetUser) {
      return { recipient: '', status: 'rejected', reason: new Error('用户不存在') }
    }
    const { to } = targetUser
    try {
      await sendCustomMessage({
        to,
        customEvent: '',
        customExts: {
          type: 'resume',
          status: 0,
          exchange_resume_record_id: exchangeResumeRecordId,
          cUserResumeLink,
        },
      })
      await sendIMTextMessage(to, greeting)
      return { recipient: to, status: 'fulfilled' }
    } catch (error) {
      return { recipient: to, status: 'rejected', reason: error }
    }
  }
  /** 批量发送简历消息 */
  const sendMassResumeMessage = async (params: SendMassResumeMessageParams) => {
    const loadingManager = new LoadingManager()
    try {
      const { data: fileResume } = await resumeQueryMyFileResumeList()
      if (!fileResume?.length) {
        throw new Error('请先完善简历信息')
      }
      const [{ fileId: attachmentId, fileUrl: attachmentUrl }] = fileResume
      const cUserResumeLink = sm4Encrypt(attachmentUrl)
      const { greeting, sendList } = params
      const {
        data: { batchNo, data: sendResumeList },
      } = await exchangeResumeRecordBatchSendResume({
        attachmentId,
        greeting,
        sendList: sendList.map(({ to, ...params }) => params),
      })
      if (!sendResumeList?.length) return 0
      const total = sendResumeList.length
      const failed: string[] = []
      loadingManager.showProgress(total, failed)
      const MAX_CONCURRENT = 3
      const limit = pLimit(MAX_CONCURRENT)
      const tasks = sendResumeList.map(({ exchangeResumeRecordId, hrUserId }) =>
        limit(() =>
          sendSingleResumeMessage(
            exchangeResumeRecordId,
            hrUserId,
            sendList,
            greeting,
            cUserResumeLink,
          ),
        ),
      )
      const results = await Promise.all(tasks)
      const sent = results.filter((r) => r.status === 'fulfilled').length
      loadingManager.hide()
      if (sent > 0) {
        await exchangeResumeRecordBatchSendResumeCallback({ batchNo })
        uni.showToast({ title: '投递成功', icon: 'none', duration: 2000 })
      } else {
        uni.showToast({ title: '投递失败', icon: 'none', duration: 2000 })
      }
      return sent
    } catch (error) {
      loadingManager.hide()
      const message = error instanceof Error ? error.message : '投递失败'
      uni.showToast({ title: message, icon: 'none', duration: 2000 })
      return 0
    }
  }
  /** 清除会话免打扰状态 */
  const clearConversationSilentMode = async (conversationId: string) => {
    const currentConversation = getLocalConversationById(conversationId)
    if (currentConversation) {
      uni.$UIKit.convStore.clearRemindTypeForConversation(currentConversation)
    }
  }
  /** 设置会话免打扰状态 */
  const setConversationSilentMode = async (conversationId: string) => {
    const currentConversation = getLocalConversationById(conversationId)
    if (currentConversation) {
      uni.$UIKit.convStore.setSilentModeForConversation(currentConversation)
    }
  }
  /** 删除会话并设置免打扰 */
  const deleteConversation = async (conversationId: string) => {
    setConversationSilentMode(conversationId)
    await uni.$UIKit.getChatConn().deleteConversation({
      channel: conversationId,
      chatType: 'singleChat',
      deleteRoam: false,
    })
    const idx = uni.$UIKit.convStore.conversationList.findIndex(
      (cvs) => cvs.conversationType === 'singleChat' && cvs.conversationId === conversationId,
    )
    if (idx > -1) {
      uni.$UIKit.convStore.conversationList.splice(idx, 1)
    }
    clearConversationMarkQueue(conversationId)
    const localIdx = conversationList.value.findIndex((c) => c.conversationId === conversationId)
    if (localIdx > -1) {
      conversationList.value.splice(localIdx, 1)
      totalUnreadCount.value = calculateUnreadCount(conversationList.value)
      updateAppBadge(totalUnreadCount.value)
    }
  }
  /** 获取指定标记的会话列表 */
  const getConversationsByMark = async (mark: ConversationMarkType, pageSize = 50) => {
    const allConversations: ConversationWithUserInfo[] = []
    const seenIds = new Set<string>()
    let cursor = ''

    try {
      while (true) {
        const result = await uni.$UIKit.getChatConn().getServerConversationsByFilter({
          pageSize,
          cursor,
          filter: { mark },
        })
        const { conversations = [], cursor: nextCursor = '' } = result.data || {}

        // 确保黑名单是最新的
        const blacklist = await updateBlacklistUsers()

        const validConversations = conversations
          .filter((item) => {
            if (blacklist.includes(item.conversationId)) {
              return false
            }
            if (seenIds.has(item.conversationId)) {
              return false
            }
            seenIds.add(item.conversationId)
            return true
          })
          .map((item) => {
            const { conversationId } = item
            const user = uni.$UIKit.appUserStore.getUserInfoFromStore(conversationId)
            return {
              ...item,
              ...user,
            } as ConversationWithUserInfo
          })

        allConversations.push(...validConversations)
        if (!nextCursor || validConversations.length === 0) {
          console.log(
            `获取标记 ${mark} 的会话完成，共 ${allConversations.length} 条`,
            allConversations,
          )
          break
        }
        cursor = nextCursor
      }
    } catch (error) {
      console.error('获取会话列表失败', error)
    }
    return allConversations
  }

  const calculateUnreadCount = (conversations: ConversationWithUserInfo[]) => {
    return conversations.reduce((total, conversation) => {
      return total + (conversation.unReadCount || 0)
    }, 0)
  }

  const shouldUpdateMarkForOfflineMessage = (
    conversation: UIKITConversationItem,
    currentUserId: string,
  ) => {
    const { lastMessage, marks = [] } = conversation

    const hasNewGreetingMark = marks.includes(
      CONVERSATION_MARKS.NEW_GREETING as ConversationMarkType,
    )
    const hasHigherPriorityMark = marks.some((mark) =>
      HIGH_PRIORITY_MARKS.includes(mark as ConversationMarkType),
    )
    if (hasNewGreetingMark || hasHigherPriorityMark) return false
    const hasOnlyChatMark = marks.includes(CONVERSATION_MARKS.ONLY_CHAT as ConversationMarkType)
    if (hasOnlyChatMark) return false
    const isInitiatedByMe = conversation.lastMessage?.from === currentUserId
    return lastMessage && lastMessage.from && lastMessage.from !== currentUserId && !isInitiatedByMe
  }

  const enrichConversationsWithUserInfo = (conversations: UIKITConversationItem[]) => {
    return conversations.map((item) => {
      const { conversationId } = item
      const user = uni.$UIKit.appUserStore.getUserInfoFromStore(conversationId)
      return {
        ...item,
        ...user,
      } as ConversationWithUserInfo
    })
  }

  const batchUpdateOfflineMessageMarks = async (conversations: UIKITConversationItem[]) => {
    const currentUserId = getIMLoginId.value
    const conversationsToUpdate = conversations.filter((conversation) =>
      shouldUpdateMarkForOfflineMessage(conversation, currentUserId),
    )
    if (conversationsToUpdate.length === 0) return
    const batches = []
    for (let i = 0; i < conversationsToUpdate.length; i += LOAD_CONVERSATIONS_CONFIG.BATCH_SIZE) {
      batches.push(conversationsToUpdate.slice(i, i + LOAD_CONVERSATIONS_CONFIG.BATCH_SIZE))
    }
    for (const batch of batches) {
      const markPromises = batch.map(async ({ conversationId }) => {
        try {
          await smartUpdateConversationMark(conversationId, CONVERSATION_MARKS.NEW_GREETING)
          console.log(`为会话 ${conversationId} 设置 NEW_GREETING 标记（离线消息）`)
        } catch (error) {
          console.error(`设置离线消息标记失败 ${conversationId}:`, error)
        }
      })
      await Promise.allSettled(markPromises)
    }
  }

  /** 应用角标更新 */
  const updateAppBadge = (count: number) => {
    // #ifdef APP-PLUS
    try {
      plus.runtime.setBadgeNumber(count)
    } catch (error) {
      console.error('更新应用角标失败:', error)
    }
    // #endif
  }

  return {
    conversationList,
    totalUnreadCount,
    customCardInfo,
    customBusinessCardInfo,
    loadConversations,
    getLocalConversationById,
    listenForNewMessages,
    listenForPushNotifications,
    closePushNotifications,
    deleteConversation,
    setConversationSilentMode,
    clearConversationSilentMode,
    getLastTypeMessage,
    getIMUserInfo,
    getIMUserInfoSelf,
    getConversationInfo,
    getIMLoginId,
    sendIMTextMessage,
    sendGreetingMessage,
    sendResumeMessage,
    sendMassResumeMessage,
    sendCustomResumeMessage,
    sendCustomPhoneMessage,
    sendCustomWeChatCodeMessage,
    sendInvitationForInterview,
    setConversationMark,
    removeConversationMark,
    getConversationsByMark,
    autoUpdateConversationMark,
    processHistoryMarkSyncMessages,
    smartUpdateConversationMark,
    clearMarkQueues,
    updateBlacklistUsers,
    unInterestBlackUserNameList,
    updateImUsersBlocklist,
    toggleImUserBlockList,
    isUserInImBlockList,
  }
}
