<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging :paging-style="pageStyle" layout-only>
    <template #top>
      <CustomNavBar title="修改密码"></CustomNavBar>
    </template>
    <view class="setting">
      <!-- 原手机号码 -->
      <view class="setting-list border-b">
        <view class="list-item-text text-32rpx">手机号码</view>
        <view class="input-container">
          <wd-input
            v-model="fromData.maskedPhone"
            disabled
            no-border
            placeholder="请输入旧手机号码"
          />
        </view>
      </view>
      <!-- 验证码 -->
      <view class="setting-list border-b">
        <view class="list-item-text text-32rpx">验证码</view>
        <view class="input-container">
          <wd-input
            v-model="fromData.captchaVerifyParam"
            :maxlength="4"
            no-border
            placeholder="请输入验证码"
          />
          <view
            :class="{ 'code-btn-disabled': verificationCode > 0 }"
            class="code-btn"
            @click="getNewCode"
          >
            {{ verificationCode > 0 ? `${verificationCode}s` : '获取验证码' }}
          </view>
        </view>
      </view>
      <!--新密码-->
      <view class="setting-list border-b">
        <view class="list-item-text text-32rpx">新密码</view>
        <view class="input-container">
          <wd-input
            v-model="fromData.password"
            :maxlength="30"
            no-border
            show-password
            placeholder="请设置新密码"
          />
        </view>
      </view>
      <!--请再次确认-->
      <view class="setting-list border-b">
        <view class="list-item-text text-32rpx">确认密码</view>
        <view class="input-container">
          <wd-input
            v-model="fromData.confirmAgainPassword"
            show-password
            :maxlength="30"
            no-border
            placeholder="请再次输入新密码"
          />
        </view>
      </view>
    </view>
  </z-paging>

  <!-- 底部固定按钮 -->
  <view class="btn-fixed" @click="confirmChange">
    <view class="btn_box">
      <view class="btn_bg">确认修改</view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { useUserStore } from '@/store'
import { sendUpdatePasswordCode, updateMyPassword } from '@/interPost/my'
import { maskPhoneNumber } from '@/utils/common'

const { sm4Encrypt } = useSmCrypto({
  type: 'password',
})

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

const fromData = ref({
  maskedPhone: '',
  password: '',
  captchaVerifyParam: '',
  confirmAgainPassword: '',
})

// 获取用户store
const userStore = useUserStore()
const verificationCode = ref(0)

// 倒计时定时器
let verificationTimer: number | null = null

// 计算脱敏后的手机号
const maskedPhone = computed(() => {
  const phone = userStore.userInfo?.phone
  if (phone) {
    return maskPhoneNumber(phone)
  }
  return '未设置'
})

// 开始倒计时
const startCountdown = () => {
  verificationCode.value = 60
  const interval = setInterval(() => {
    verificationCode.value--
    if (verificationCode.value <= 0) {
      clearInterval(interval)
      verificationTimer = null
    }
  }, 1000)
  verificationTimer = interval
}

const getNewCode = async () => {
  // 如果正在倒计时，不允许点击
  if (verificationCode.value > 0) {
    return
  }

  const res: any = await sendUpdatePasswordCode()
  if (res.code === 0) {
    uni.showToast({
      title: '验证码发送成功',
      icon: 'success',
      duration: 2000,
    })
    // 开始倒计时
    startCountdown()
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'error',
      duration: 2000,
    })
  }
}

// 确认修改
const confirmChange = async () => {
  if (fromData.value.password === '') {
    uni.showToast({
      title: '请输入新密码',
      icon: 'none',
      duration: 2000,
    })
    return
  }
  if (fromData.value.password.length < 6) {
    uni.showToast({
      title: '密码最小长度为6位',
      icon: 'none',
      duration: 2000,
    })
    return
  }
  if (fromData.value.password !== fromData.value.confirmAgainPassword) {
    uni.showToast({
      title: '两次输入的密码不一致',
      icon: 'none',
      duration: 2000,
    })
    return
  }
  if (fromData.value.captchaVerifyParam === '') {
    uni.showToast({
      title: '请输入手机验证码',
      icon: 'none',
      duration: 2000,
    })
    return
  }
  const password = await sm4Encrypt(fromData.value.password)
  const res: any = await updateMyPassword({
    password,
    captchaVerifyParam: fromData.value.captchaVerifyParam,
  })
  if (res.code === 0) {
    uni.showToast({
      title: '修改成功，请重新登录',
      icon: 'none',
      duration: 2000,
    })
    // 清空用户信息
    userStore.clearUserInfo()
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/login/index',
      })
    }, 2000)
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 2000,
    })
  }
}

onShow(() => {
  fromData.value.maskedPhone = maskedPhone.value
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (verificationTimer) {
    clearInterval(verificationTimer)
  }
})
</script>
<style lang="scss" scoped>
:deep(.wd-input) {
  width: 100%;
  text-align: left;
  background-color: transparent;
}

:deep(.wd-input__placeholder) {
  font-size: 28rpx !important;
  color: #888888;
}

:deep(.wd-picker__placeholder) {
  font-size: 28rpx !important;
  color: #888888;
}

:deep(.wd-input__inner) {
  font-size: 32rpx !important;
  font-weight: 500;
}

:deep(.uni-input-input) {
  font-size: 32rpx !important;
  font-weight: normal !important;
  color: #333333 !important;
}

:deep(.wd-input__placeholder) {
  font-size: 28rpx !important;
  font-weight: normal !important;
}

.setting {
  padding: 0rpx 40rpx;
  padding-bottom: 200rpx; // 为底部按钮留出空间

  .setting-list {
    padding: 30rpx 20rpx 30rpx 20rpx;

    .list-item-text {
      margin-bottom: 20rpx;
      color: #333;
    }

    .input-container {
      display: flex;
      gap: 20rpx;
      align-items: center;

      .wd-input {
        flex: 1;
      }

      .code-btn {
        padding: 12rpx 24rpx;
        margin-right: -20rpx;
        font-size: 28rpx;
        color: #333;
        white-space: nowrap;
        cursor: pointer;
        border-radius: 8rpx;
        transition: all 0.3s ease;

        &.code-btn-disabled {
          display: flex;
          align-items: center;
          justify-content: center;
          min-width: 100rpx;
          height: 52rpx;
          color: #999999;
          cursor: not-allowed;
          background: #cccccc;
        }
      }
    }
  }

  .row-main {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .phone-arrow {
    display: flex;
    align-items: center;

    .phone-text {
      margin-right: 10rpx;
      font-size: 28rpx;
      color: #888;
    }
  }

  .tip-text {
    margin-top: 8rpx;
    margin-left: 2rpx;
    font-size: 22rpx;
    line-height: 1.4;
    color: #999999;
  }
}

// 底部固定按钮样式
.btn-fixed {
  position: fixed;
  right: 0;
  bottom: 48rpx;
  left: 0;
  z-index: 10;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  padding: 0rpx 40rpx;

  .btn_box {
    box-sizing: border-box;
    width: 100%;

    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 32rpx;
      font-weight: normal;
      color: #333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
</style>
