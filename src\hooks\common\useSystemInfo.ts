export const useSystemInfo = () => {
  const sysInfo = uni.getSystemInfoSync()
  const sysSafeAreaInsets = computed(() => sysInfo.safeAreaInsets)
  const sysStatusBarHeight = computed(() => sysInfo.statusBarHeight)
  const sysScreenWidth = computed(() => sysInfo.screenWidth || 375)
  const sysAppPlatform = computed(() => sysInfo.platform)
  return {
    sysInfo,
    sysSafeAreaInsets,
    sysStatusBarHeight,
    sysScreenWidth,
    sysAppPlatform,
  }
}
