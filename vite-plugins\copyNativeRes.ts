import fs from 'fs-extra'
import path from 'path'

export function copyNativeRes() {
  const nativeResourcesPath = path.resolve(__dirname, '../src/nativeResources')
  const androidPrivacyPath = path.resolve(__dirname, '../src/androidPrivacy.json')

  const buildPath = path.resolve(
    __dirname,
    '../dist',
    process.env.NODE_ENV === 'production' ? 'build' : 'dev',
    process.env.UNI_PLATFORM!,
  )

  return {
    enforce: 'post',
    async writeBundle() {
      try {
        // 复制 nativeResources 目录（如果存在）
        const nativeResourcesExists = await fs.pathExists(nativeResourcesPath)
        if (nativeResourcesExists) {
          const nativeResourcesBuildPath = path.join(buildPath, 'nativeResources')
          await fs.ensureDir(nativeResourcesBuildPath)
          await fs.copy(nativeResourcesPath, nativeResourcesBuildPath)
          console.log(
            `[copyNativeRes] 成功将 nativeResources 目录中的资源移动到构建目录：${nativeResourcesBuildPath}`,
          )
        }

        // 复制 androidPrivacy.json 文件
        const androidPrivacyExists = await fs.pathExists(androidPrivacyPath)
        if (androidPrivacyExists) {
          const androidPrivacyBuildPath = path.join(buildPath, 'androidPrivacy.json')
          await fs.copy(androidPrivacyPath, androidPrivacyBuildPath)
          console.log(
            `[copyNativeRes] 成功将 androidPrivacy.json 复制到构建目录：${androidPrivacyBuildPath}`,
          )
        } else {
          console.warn(
            `[copyNativeRes] 警告：androidPrivacy.json 文件不存在：${androidPrivacyPath}`,
          )
        }
      } catch (error) {
        console.error(`[copyNativeRes] 复制资源失败：`, error)
      }
    },
  }
}
