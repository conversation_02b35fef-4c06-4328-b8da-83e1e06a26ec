import { storeToRefs } from 'pinia'
import { useUserStore } from '@/store'

/** 用户信息hooks */
export const useUserInfo = () => {
  const { userInfo, isLoginEd: getUserIsLogin } = storeToRefs(useUserStore())
  const {
    setUserInfo: setUserIntel,
    setUserRoleType,
    setUserToken,
    getToken,
    getUserLoginTimeDiff,
    clearUserInfo,
    getHrExamineStateObj,
    setHrExamineStateObj,
    setUserHasShownAgreement,
    getUserHasShownAgreement,
  } = useUserStore()

  /** 用户信息 */
  const userIntel = computed(() => userInfo.value)
  /** 是否需要刷新token */
  const needRefreshToken = computed(() => getUserLoginTimeDiff() >= 1)
  /** 当前用户类型是否为企业 */
  const userRoleIsBusiness = computed(() => userIntel.value.type === 1)
  /** 当前用户是否实名 */
  const userRoleIsRealName = computed(() => !!userIntel.value.isAuth)
  // hr审核状态
  const hrExamineStateObj = computed(() => getHrExamineStateObj())
  // 是否完成大步骤
  const isCompleteBigStep = computed(() => !!userIntel.value?.requiredFinishStatus)
  /** 是否已同意隐私协议 */
  const hasShownAgreement = computed(() => getUserHasShownAgreement())
  return {
    /** 用户信息 */
    userIntel,
    /** 当前用户类型是否为企业 */
    userRoleIsBusiness,
    /** 当前用户是否实名 */
    userRoleIsRealName,
    /** 设置用户信息 */
    setUserIntel,
    /** 更新用户角色 */
    setUserRoleType,
    /** 获取用户是否登录 */
    getUserIsLogin,
    /** 设置用户令牌 */
    setUserToken,
    /** 获取用户令牌 */
    getToken,
    /** 清除用户信息 */
    clearUserInfo,
    /** 是否需要刷新token */
    needRefreshToken,
    /** 是否完成大步骤 */
    isCompleteBigStep,
    /** 获取hr审核状态 */
    getHrExamineStateObj,
    /** hr审核状态 */
    hrExamineStateObj,
    setHrExamineStateObj,
    /** 是否已同意隐私协议 */
    hasShownAgreement,
    /** 设置是否已同意隐私协议 */
    setUserHasShownAgreement,
  }
}
