<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging ref="pagingRef" layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar></CustomNavBar>
    </template>
    <view class="agreement-container">
      <view class="header">
        <text class="title">易直聘申请使用权限列表</text>
      </view>
      <view class="content">
        <text class="paragraph"></text>
        <text class="paragraph">
          1、为保障易直聘产品功能的实现及安全稳定运行，我们可能会申请或使用操作系统的相关权限。
        </text>
        <text class="paragraph">
          2、为保障您的知情权，我们通过下列列表将产品可能申请、使用的相关操作系统权限进行展示，您可以根据实际需要对相关权限进行管理。
        </text>
        <text class="paragraph">
          3、根据产品升级需求，所申请、使用权限的类型及目的可能发生变动，我们将根据上述变动及时调整列表内容，确保您能及时知悉权限的申请与使用情况。
        </text>
        <text class="paragraph">
          4、请您知悉，为满足业务及产品的功能与安全需求，我们可能也会使用第三方SDK，这些第三方也可能会申请或使用相关操作系统权限。
        </text>
        <text class="paragraph">
          5、在产品使用过程中，若您使用第三方开发的H5页面或小程序，这些第三方开发的插件或小程序也可能因业务功能所必需而申请或使用相关操作系统权限。
        </text>

        <view class="table-section">
          <text class="section-title">安卓操作系统应用权限列表</text>

          <view class="table-container">
            <scroll-view :scroll-x="true" class="table-scroll">
              <view class="table">
                <!-- 表头 -->
                <view class="table-header">
                  <view class="table-cell header-cell permissionName">权限名称</view>
                  <view class="table-cell header-cell permissionDesc">权限功能说明</view>
                  <view class="table-cell header-cell shareMethod">使用场景或目的</view>
                </view>

                <!-- 表格数据 -->
                <view class="table-row" v-for="(item, index) in AndroidList" :key="index">
                  <view class="table-cell permissionName">{{ item.permissionName }}</view>
                  <view class="table-cell permissionDesc">{{ item.permissionDesc }}</view>
                  <view class="table-cell shareMethod">{{ item.shareMethod }}</view>
                </view>
              </view>
            </scroll-view>
          </view>

          <text class="section-title m-t-64rpx">iOS操作权限应用权限列表</text>

          <view class="table-container">
            <scroll-view :scroll-x="true" class="table-scroll">
              <view class="table">
                <!-- 表头 -->
                <view class="table-header">
                  <view class="table-cell header-cell permissionName">权限名称</view>
                  <view class="table-cell header-cell permissionDesc">权限功能说明</view>
                  <view class="table-cell header-cell shareMethod">使用场景或目的</view>
                </view>

                <!-- 表格数据 -->
                <view class="table-row" v-for="(item, index) in IOSList" :key="index">
                  <view class="table-cell permissionName">{{ item.permissionName }}</view>
                  <view class="table-cell permissionDesc">{{ item.permissionDesc }}</view>
                  <view class="table-cell shareMethod">{{ item.shareMethod }}</view>
                </view>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

// 添加 z-paging 引用
const pagingRef = ref(null)

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

const AndroidList = ref([
  {
    permissionName: 'ACCESS_FINE_LOCATION–精确定位（GPS）',
    permissionDesc: '使用全球定位系统（GPS）或网络位置信息（例如基站和WLAN）获取精准地理位置信息',
    shareMethod:
      '用于为求职者推荐周边职位、所在区域位置、地铁线路，提供地图选点、路线计算、面试及签到服务；同时保障招聘者身份与职位信息的真实性和有效性，确保招聘与求职过程的安全。',
  },
  {
    permissionName: 'ACCESS_COARSE_LOCATION–粗略定位（Wi-Fi、基站）',
    permissionDesc: '通过网络位置信息（例如基站和WLAN）获取大致地理位置信息',
    shareMethod:
      '用于为求职者推荐周边职位、所在区域位置、地铁线路，提供地图选点、路线计算、面试及签到服务；同时保障招聘者身份与职位信息的真实性和有效性，确保招聘与求职过程的安全。',
  },
  {
    permissionName: 'CAMERA–使用相机',
    permissionDesc: '拍摄照片和视频、扫描二维码',
    shareMethod: '用于扫码登录、拍摄，并上传照片/视频、招聘者身份认证',
  },
  {
    permissionName: 'android.hardware.camera–需要相机硬件',
    permissionDesc: '拍摄照片和视频、扫描二维码',
    shareMethod: '用于扫码登录、拍摄，并上传照片/视频、招聘者身份认证',
  },
  {
    permissionName: 'android.hardware.camera.autofocus–需要自动对焦功能',
    permissionDesc: '拍摄照片和视频、扫描二维码',
    shareMethod: '用于扫码登录、拍摄，并上传照片/视频、招聘者身份认证',
  },
  {
    permissionName: 'READ_EXTERNAL_STORAGE–读取外部存储',
    permissionDesc: '提供读取手机储存空间内数据的功能',
    shareMethod:
      '允许App读取设备存储中的图片、文件等内容，主要用于协助您发布信息，以及在本地记录崩溃日志信息（如有）等功能',
  },
  {
    permissionName: 'WRITE_EXTERNAL_STORAGE–写入外部存储（如保存文件）',
    permissionDesc: '提供写入外部储存功能',
    shareMethod: '允许App写入/下载/保存/修改/删除图片、文件、崩溃日志等信息',
  },
  {
    permissionName: 'READ_MEDIA_IMAGES–读取图片',
    permissionDesc: '提供读取手机储存空间内数据的功能',
    shareMethod:
      '允许App读取设备存储中的图片、文件等内容，主要用于协助您发布信息，以及在本地记录崩溃日志信息（如有）等功能',
  },
  {
    permissionName: 'READ_MEDIA_VIDEO–读取视频',
    permissionDesc: '提供读取手机储存空间内数据的功能',
    shareMethod:
      '允许App读取设备存储中的图片、文件等内容，主要用于协助您发布信息，以及在本地记录崩溃日志信息（如有）等功能',
  },
  {
    permissionName: 'READ_MEDIA_AUDIO–读取音频',
    permissionDesc: '提供读取手机储存空间内数据的功能',
    shareMethod:
      '允许App读取设备存储中的图片、文件等内容，主要用于协助您发布信息，以及在本地记录崩溃日志信息（如有）等功能',
  },
  {
    permissionName: 'INTERNET–访问网络',
    permissionDesc: '提供APP访问网络的能力，保障各类基于网络的功能得以正常运行',
    shareMethod:
      '用于职位信息获取与更新、在线沟通交流、用户资料同步与更新、数据统计与分析、版本更新检查与下载等功能',
  },
  {
    permissionName: 'ACCESS_NETWORK_STATE–获取网络连接状态',
    permissionDesc: '提供APP获取设备当前的网络连接状态信息的功能',
    shareMethod:
      '用于优化数据加载策略、保障功能正常运行、智能切换网络提示、辅助网络相关功能决策、提升应用稳定性与效率等功能',
  },
  {
    permissionName: 'ACCESS_WIFI_STATE–获取Wi-Fi状态',
    permissionDesc: '提供APP获取设备当前的Wi-Fi连接状态信息的功能',
    shareMethod:
      '用于流量优化与数据加载、网络状态监控与提示、辅助网络诊断与优化、智能功能推荐与引导、应用性能与资源管理等功能',
  },
  {
    permissionName: 'CHANGE_WIFI_STATE–修改Wi-Fi状态（例如连接网络）',
    permissionDesc: '提供APP修改设备Wi-Fi状态的能力的功能',
    shareMethod:
      '用于优化网络连接体验、自动网络配置辅助、网络安全与节能管理、网络环境适配与测试、引导用户改善网络状况等功能',
  },
  {
    permissionName: 'CHANGE_NETWORK_STATE–修改网络状态',
    permissionDesc: '提供APP修改设备网络状态的能力的功能',
    shareMethod:
      '用于保障招聘求职关键环节网络畅通、适配多样化网络环境、提升网络安全与节能管理、优化应用性能与功能测试、辅助用户解决网络问题等功能',
  },
  {
    permissionName: 'READ_PHONE_STATE–获取设备状态（如IMEI、信号）',
    permissionDesc: '提供APP能够获取设备的特定状态信息（如IMEI、信号）的功能',
    shareMethod:
      '用于保障账号安全与登录识别、优化网络相关服务、设备故障诊断与技术支持、统计分析与服务优化等功能',
  },
  {
    permissionName: 'VIBRATE–控制震动',
    permissionDesc: '提供APP控制设备的震动功能',
    shareMethod: '用于消息通知提醒，通过震动的方式向用户传递特定的提示信息',
  },
  {
    permissionName: 'WAKE_LOCK–保持设备唤醒（防止休眠）',
    permissionDesc: '提供APP获取设备的唤醒锁的功能',
    shareMethod:
      '用于实时消息交互时、简历上传或职位发布过程中、面试签到环节等，保障相关功能的连续正常运行',
  },
])
const IOSList = ref([
  {
    permissionName: 'NSPhotoLibraryUsageDescription:需要访问您的相册，用于上传、选择图片。',
    permissionDesc: '访问相册',
    shareMethod: '用于支持存储中图片和视频的访问、发送、上传',
  },
  {
    permissionName: 'NSPhotoLibraryAddUsageDescription:需要保存图片或视频到您的相册。',
    permissionDesc: '向相册中添加内容',
    shareMethod: '用于支持App写入/下载/保存/修改/删除图片、视频',
  },
  {
    permissionName: 'NSLocationWhenInUseUsageDescription:需要获取您的位置，用于定位功能。',
    permissionDesc: '仅在使用期间访问位置',
    shareMethod:
      '用于为求职者推荐附近的职位、区域位置或地铁线路、地图选点、计算路线、面试及签到；保障招聘者身份和职位信息真实有效，确保招聘求职安全',
  },
  {
    permissionName: 'NSCameraUsageDescription:需要使用您的相机，用于拍摄和上传照片。',
    permissionDesc: '使用摄像头',
    shareMethod: '用于扫码登录、拍摄并上传照片/视频、开展视频招聘/面试、招聘者身份认证',
  },
  {
    permissionName: 'NSMicrophoneUsageDescription:需要使用您的麦克风，用于录制音频或视频。',
    permissionDesc: '使用麦克风',
    shareMethod:
      '用于使用视频面试、语音通话、发送语音消息、视频招聘会中在线连麦、招聘者实名认证及招聘者环境认证中与工作人员通话',
  },
])
</script>

<style lang="scss" scoped>
.agreement-container {
  padding: 20rpx 40rpx;
}

.header {
  margin-bottom: 30rpx;
  text-align: center;
}

.title {
  display: block;
  margin-bottom: 10rpx;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.content {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333333;
}

.paragraph {
  display: block;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  color: #333333;
  text-align: justify;
}

.table-section {
  margin-top: 40rpx;
}

.section-title {
  display: block;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.section-subtitle {
  display: block;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #666666;
}

.table-container {
  overflow: hidden;
  background: #ffffff;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
}

.table-scroll {
  width: 100%;
}

.table {
  width: 100%;
  min-width: 1200rpx;
}

.table-header {
  display: flex;
  background: #f5f5f5;
  border-bottom: 1rpx solid #e0e0e0;
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid #e0e0e0;

  &:last-child {
    border-bottom: none;
  }

  &:nth-child(even) {
    background: #fafafa;
  }
}

.table-cell {
  padding: 20rpx 15rpx;
  font-size: 24rpx;
  line-height: 1.4;
  word-break: break-all;
  border-right: 1rpx solid #e0e0e0;

  &:last-child {
    border-right: none;
  }

  &.header-cell {
    font-weight: bold;
    color: #333333;
    background: #f5f5f5;
  }

  &.permissionName {
    width: 200rpx;
    min-width: 200rpx;
  }

  &.permissionDesc {
    width: 200rpx;
    min-width: 200rpx;
  }

  &.purpose {
    width: 200rpx;
    min-width: 200rpx;
  }

  &.info-type {
    width: 250rpx;
    min-width: 250rpx;
  }

  &.shareMethod {
    width: 260rpx;
    min-width: 260rpx;
  }

  &.rules {
    width: 280rpx;
    min-width: 280rpx;
  }
}

.rules-text {
  word-break: break-all;
  white-space: pre-line;
}
</style>
