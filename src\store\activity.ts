import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useActivityStore = defineStore(
  'activity',
  () => {
    // 存储每个用户的displayActivityTotal增量
    const activityIncrements = ref<Record<string, number>>({})
    
    // 动画状态管理
    const animationStates = ref<Record<string, boolean>>({})
    
    /**
     * 记录用户活动（企业端进入卡片时调用）
     * @param userId 用户ID
     */
    const recordActivity = (userId: string) => {
      if (!userId) return
      
      if (!activityIncrements.value[userId]) {
        activityIncrements.value[userId] = 0
      }
      // 这里只是记录，不增加数值，实际增加在返回时触发
    }
    
    /**
     * 增加用户活动总数（从预览页面返回时调用）
     * @param userId 用户ID
     * @returns 返回是否触发了动画
     */
    const incrementActivity = (userId: string): boolean => {
      if (!userId) return false
      
      // 增加计数
      if (!activityIncrements.value[userId]) {
        activityIncrements.value[userId] = 0
      }
      activityIncrements.value[userId]++
      
      // 触发动画
      animationStates.value[userId] = true
      
      // 1.5秒后关闭动画
      setTimeout(() => {
        animationStates.value[userId] = false
      }, 1500)
      
      return true
    }
    
    /**
     * 获取用户的活动增量
     * @param userId 用户ID
     * @returns 增量值
     */
    const getActivityIncrement = (userId: string): number => {
      return activityIncrements.value[userId] || 0
    }
    
    /**
     * 获取用户的动画状态
     * @param userId 用户ID
     * @returns 是否正在播放动画
     */
    const getAnimationState = (userId: string): boolean => {
      return animationStates.value[userId] || false
    }
    
    /**
     * 清空所有活动数据（分页刷新时调用）
     */
    const clearAllActivities = () => {
      activityIncrements.value = {}
      animationStates.value = {}
    }
    
    /**
     * 清空特定用户的活动数据
     * @param userId 用户ID
     */
    const clearUserActivity = (userId: string) => {
      if (userId) {
        delete activityIncrements.value[userId]
        delete animationStates.value[userId]
      }
    }
    
    return {
      activityIncrements,
      animationStates,
      recordActivity,
      incrementActivity,
      getActivityIncrement,
      getAnimationState,
      clearAllActivities,
      clearUserActivity,
    }
  },
  {
    persist: false, // 不持久化，每次重新进入应用都重置
  },
)
