import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useActivityStore = defineStore(
  'activity',
  () => {
    // 存储每个用户的displayActivityTotal增量
    const activityIncrements = ref<Record<string, number>>({})
    // 动画状态管理
    const animationStates = ref<Record<string, boolean>>({})

    // 缓存每个用户的基础竞争力值，避免重复计算和闪动
    const baseActivityTotals = ref<Record<string, number>>({})
    /**
     * 获取或生成用户的基础竞争力值
     * @param userId 用户ID
     * @param serverTotal 服务器返回的实际值（如果有）
     * @returns 基础竞争力值
     */
    const getOrCreateBaseTotal = (userId: string, serverTotal?: number): number => {
      if (!userId) return 150

      // 如果服务器有实际值，使用服务器值
      if (serverTotal && serverTotal > 0) {
        baseActivityTotals.value[userId] = serverTotal > 999 ? 999 : serverTotal
        return baseActivityTotals.value[userId]
      }

      // 如果已经缓存了基础值，直接返回
      if (baseActivityTotals.value[userId]) {
        return baseActivityTotals.value[userId]
      }

      // 生成固定的伪随机基础值
      const seed = userId
        .toString()
        .split('')
        .reduce((acc, char) => acc + char.charCodeAt(0), 0)
      const pseudoRandom = ((seed * 9301 + 49297) % 233280) / 233280
      const baseValue = Math.floor(pseudoRandom * 101) + 100 // 100-200之间的固定值

      baseActivityTotals.value[userId] = baseValue
      return baseValue
    }

    /**
     * 记录用户活动（企业端进入卡片时调用）
     * @param userId 用户ID
     */
    const recordActivity = (userId: string) => {
      if (!userId) return

      if (!activityIncrements.value[userId]) {
        activityIncrements.value[userId] = 0
      }
      // 这里只是记录，不增加数值，实际增加在返回时触发
    }
    /**
     * 增加用户活动总数（从预览页面返回时调用）
     * @param userId 用户ID
     * @returns 返回是否触发了动画
     */
    const incrementActivity = (userId: string): boolean => {
      if (!userId) return false

      // 增加计数
      if (!activityIncrements.value[userId]) {
        activityIncrements.value[userId] = 0
      }
      activityIncrements.value[userId]++

      // 触发动画
      animationStates.value[userId] = true

      // 1.5秒后关闭动画
      setTimeout(() => {
        animationStates.value[userId] = false
      }, 1500)

      return true
    }

    /**
     * 获取用户的活动增量
     * @param userId 用户ID
     * @returns 增量值
     */
    const getActivityIncrement = (userId: string): number => {
      return activityIncrements.value[userId] || 0
    }

    /**
     * 获取用户的动画状态
     * @param userId 用户ID
     * @returns 是否正在播放动画
     */
    const getAnimationState = (userId: string): boolean => {
      return animationStates.value[userId] || false
    }

    /**
     * 清空所有活动数据（分页刷新时调用）
     * 注意：不清空baseActivityTotals，保持基础值稳定
     */
    const clearAllActivities = () => {
      activityIncrements.value = {}
      animationStates.value = {}
      // 不清空 baseActivityTotals，保持基础值稳定
    }

    /**
     * 清空特定用户的活动数据
     * @param userId 用户ID
     */
    const clearUserActivity = (userId: string) => {
      if (userId) {
        delete activityIncrements.value[userId]
        delete animationStates.value[userId]
        // 不删除基础值，保持稳定
      }
    }

    return {
      activityIncrements,
      animationStates,
      baseActivityTotals,
      getOrCreateBaseTotal,
      recordActivity,
      incrementActivity,
      getActivityIncrement,
      getAnimationState,
      clearAllActivities,
      clearUserActivity,
    }
  },
  {
    persist: false, // 不持久化，每次重新进入应用都重置
  },
)
