import { POST } from '../index'
import { unInterestPositionAddDataInt, unInterestPositionCancelDataInt } from './types'
import { HttpRequestConfig } from 'luch-request'

/** 添加不感兴趣接口 */
export const unInterestPositionAdd = (
  data: unInterestPositionAddDataInt,
  config?: HttpRequestConfig,
) => POST('/easyzhipin-api/unInterestPosition/add', data, config)
/** 取消不感兴趣接口 */
export const unInterestPositionCancel = (
  data: unInterestPositionCancelDataInt,
  config?: HttpRequestConfig,
) => POST('/easyzhipin-api/unInterestPosition/cancel', data, config)
