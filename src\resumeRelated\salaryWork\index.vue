<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="bg-img">
    <CustomNavBar title="高薪岗位"></CustomNavBar>
    <view>
      <view class="page_box">
        <view class="page_flex_row">
          <view class="page_flex">
            <view class="page_flex_left text-32rpx font-500 p-t-40rpx relative">高薪岗位</view>
          </view>
          <view class="text-wrap m-t-20rpx p-t-16rpx c-#666 text-26rpx w-360rpx">
            挑战高薪岗位，成就职场精英
          </view>
          <view class="word-wrap text-24rpx w-380rpx p-t-5rpx">
            Challenge high paying positions and become workplace elites!
          </view>
        </view>
        <view class="page_flex_img"></view>
      </view>
    </view>
    <z-paging
      ref="pagingRef"
      v-model="pageData"
      :fixed="false"
      :paging-style="pageStyle"
      :style="{ height: `calc(100vh - ${customBar * 2}rpx - 120px)` }"
      safe-area-inset-bottom
      @query="queryList"
    >
      <view class="salaryWork-box">
        <JobCardList
          :job-list="pageData || []"
          @go-detail="goDetail"
          @go-job="goJob"
          @go-chat="goChat"
        />
      </view>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { highPositions } from '@/interPost/resume'
import { getCustomBar } from '@/utils/storage'
import { useMessage } from 'wot-design-uni'
import { ChatUIKit } from '@/ChatUIKit/index'
import { numberTokw } from '@/utils/common'
import { truncateText } from '@/utils/util'
import jz from '@/static/img/home/<USER>'
import pk from '@/static/img/home/<USER>'
import { useLoginStore } from '@/store'
import resumeMatching from '@/static/common/resume-matching.png'
import JobCardList from '@/components/home/<USER>/index.vue'
import { get } from 'mobx'

const appUserStore = ChatUIKit.appUserStore
const { getDictLabel } = useDictionary()
const { setmyjobList } = usePosition()
const message = useMessage()
const { sendGreetingMessage, sendResumeMessage } = useIMConversation()
const { userRoleIsRealName } = useUserInfo()
const { pagingRef, pageInfo, pageData, pageStyle, pageSetInfo } = usePaging({
  style: {
    padding: '60rpx 0rpx 0rpx',
    marginTop: '30rpx',
    borderRadius: '50rpx 50rpx 0rpx 0rpx',
    boxShadow: '8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1)',
    background: '#ffffff',
  },
})
// vuex数据
const loginStore = useLoginStore()
// 参数
const params = ref({
  entity: {},
  orderBy: {},
})
const customBar = ref(null)
const queryList = async (page, size) => {
  pageSetInfo(page, size)
  const res: any = await highPositions({
    ...params.value,
    page: pageInfo.page,
    size: pageInfo.size,
  })
  if (res.code === 0) {
    // 匹配度和竞争力
    setmyjobList(res.data.list)
    // 渲染数据操作
    const processedList = await Promise.all(
      res.data.list.map(async (ele: any, index: number) => {
        // console.log(`处理第${index}条数据:`, ele)

        // 匹配度和竞争力处理
        loginStore.myjobFillterList.forEach((item) => {
          if (item.id === ele.id) {
            ele.competitiveness = item.competitiveness
            ele.matchingDegree = item.matchingDegree
            ele.count = item.count
          }
        })

        // 在线状态处理
        try {
          const eleData = await appUserStore.getUserInfoFromStore(ele.hxUserInfoVO?.username)
          if (ele.hxUserInfoVO) {
            ele.hxUserInfoVO.isOnline = !!eleData?.isOnline
          }
        } catch (error) {}
        // 头像处理
        if (!ele.hrPositionUrl) {
          ele.hrPositionUrl =
            ele.sex === 1 ? '/static/header/hrheader1.png' : '/static/header/hrheader2.png'
        }

        // 公司规模处理
        ele.sizeName = await getDictLabel(100, ele.sizeName)
        // 学历处理
        ele.workEducational = await getDictLabel(10, ele.workEducational)

        // 安全处理 positionKey，确保它是一个数组
        if (ele.positionKey && typeof ele.positionKey === 'string') {
          ele.positionKey = ele.positionKey.split(',').filter((item) => item && item.trim())
        } else if (!Array.isArray(ele.positionKey)) {
          ele.positionKey = []
        }
        // 处理工作年限
        if (
          ele.workExperienceStart === 0 &&
          ele.workExperienceEnd === 0 &&
          Array.isArray(ele.positionKey)
        ) {
          ele.positionKey.unshift('经验不限')
        } else {
          ele.positionKey.unshift(`${ele.workExperienceStart} - ${ele.workExperienceEnd}年`)
        }
        // 学历处理
        if (ele.workEducational === '全部' && Array.isArray(ele.positionKey)) {
          ele.positionKey.unshift('学历不限')
        } else {
          ele.positionKey = [ele.workEducational, ...ele.positionKey]
        }

        // 安全处理 count，确保它是一个数组
        if (!Array.isArray(ele.count)) {
          ele.count = []
        }

        // 薪资处理
        ele.workSalaryBegin =
          ele.workSalaryBegin === 0 ? '面议' : numberTokw(ele.workSalaryBegin + '')
        ele.workSalaryEnd = ele.workSalaryEnd === 0 ? '' : numberTokw(ele.workSalaryEnd + '')

        // 距离处理
        if (ele.distanceMeters) {
          const distance = Math.floor(parseInt(ele.distanceMeters) / 1000)
          if (distance === 0) {
            ele.distanceMeters = '<1km'
          } else {
            ele.distanceMeters = distance + 'km'
          }
        }

        // console.log(`第${index}条数据处理完成:`, ele)
        return ele
      }),
    )

    pagingRef.value.complete(processedList)
  }
}
// 去沟通item
// 去沟通item
const goChat = async (item: AnyObject) => {
  try {
    await userToRealName()
    const hxUserInfoVO = item?.hxUserInfoVO || {}
    if (hxUserInfoVO?.username) {
      sendGreetingMessage(hxUserInfoVO.username, item)
    }
  } catch (error) {}
}
const goDetail = (id: any, companyId: any) => {
  uni.navigateTo({
    url: `/resumeRelated/jobDetail/index?id=${id}&companyId=${companyId}`,
  })
}
const userToRealName = () => {
  if (!userRoleIsRealName.value) {
    message
      .confirm({
        title: '提示',
        msg: '请先实名认证',
      })
      .then(() => {
        uni.navigateTo({
          url: '/setting/identityAuth/index',
        })
      })
      .catch()
    return Promise.reject(new Error('请先实名认证'))
  }
  return Promise.resolve(1)
}
const goJob = async (item: AnyObject) => {
  try {
    await userToRealName()
    const hxUserInfoVO = item?.hxUserInfoVO || {}
    if (hxUserInfoVO?.username) {
      const num = await sendResumeMessage(hxUserInfoVO.username, item)
      if (!num) {
        message
          .confirm({
            title: '提示',
            msg: '请完善简历后再投递',
          })
          .then(() => {
            uni.navigateTo({
              url: '/resumeRelated/AttachmentResume/index',
            })
          })
          .catch()
      }
    }
  } catch (error) {}
}
const goFilter = () => {
  uni.navigateTo({
    url: '/resumeRelated/filter/index',
  })
}
onLoad(async () => {
  await uni.$onLaunched
  customBar.value = getCustomBar()
  pagingRef.value.reload()
})
</script>

<style lang="scss" scoped>
.word-wrap {
  word-wrap: break-word;
}

.border-top {
  border-top: 2rpx dashed #d8d8d8;
}

.progress-style {
  padding: 0rpx 10rpx;
  border: 1rpx solid #ff3636;
  border-radius: 10rpx;
}

.content_list {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;

  .content_list_left {
    // width: 80%;
    display: flex;
    flex-direction: row;
    align-items: center;
    width: calc(100% - 200rpx);
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

    .content_list_left_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      // width: 100%;
      padding: 10rpx 30rpx;
      white-space: nowrap;

      .content_list_for {
        display: inline-block;
        margin: auto;

        .content_list_border_1 {
          padding-left: 30rpx;
        }
      }
    }
  }
}

.page_flex_left::after {
  position: absolute;
  bottom: -10rpx;
  left: 0rpx;
  width: 190rpx;
  height: 16rpx;
  content: '';
  background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
  border-radius: 32rpx;
}

.content_flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 40rpx 0rpx;
  padding-bottom: 0;

  .content_search {
    display: flex;
    flex-direction: row;
    width: 100%;

    .content_search_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      padding: 15rpx 20rpx;
      color: #fff;
      background: rgba(61, 61, 61, 0.34);
      border-radius: 80rpx;
      box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

      .content_search_left {
        display: flex;
        align-items: center;
        width: 10%;

        .img-icon {
          width: 46rpx;
          height: 46rpx;
        }
      }

      .content_search_right {
        width: 90%;
        padding-left: 10rpx;
      }
    }
  }
}

.input_font {
  font-size: 22rpx;
  font-weight: 400;
  line-height: 44rpx;
  color: #777777;
}

.bg_end {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-top: 10rpx;

  .bg_right {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 132rpx;
    height: 64rpx;
    text-align: center;
    background: #fff4f4;
    border-radius: 20rpx 20rpx 20rpx 20rpx;

    .bg_right_icon {
      width: 50rpx;
      height: 50rpx;
    }
  }

  .bg_left {
    display: flex;
    flex-direction: row;
    align-items: center;

    .bg_left_icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50rpx;
    }

    .bg_left_flex {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding-left: 15rpx;

      .bg_left_name {
        font-size: 22rpx;
        font-weight: 400;
        line-height: 44rpx;
        color: #555555;
      }

      .bg_left_date {
        font-size: 20rpx;
        font-weight: 400;
        color: #999999;
        // line-height: 44rpx;
      }
    }
  }
}

.page_box {
  position: relative;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 0rpx 40rpx 0rpx;
}

.content_list_left-w {
  width: calc(100% - 100rpx);
}

.content_search-p {
  padding: 30rpx 40rpx;
}

.stateType {
  padding: 0rpx 10rpx;
  margin-left: 10rpx;
  font-size: 20rpx;
  color: #888888;
  text-align: center;
  border: 1rpx solid #888888;

  border-radius: 6rpx;
}

.sx {
  background: #adbaff;
}

.jz {
  background: #fda283;
}

.job-tag {
  padding: 0 20rpx;
  margin-right: 10rpx;
  font-size: 24rpx;
  color: #fff;
  background: #ff5151;
  border-radius: 20rpx 0rpx 20rpx 0rpx;
}

.content_search-p-t {
  padding: 0rpx 40rpx 0rpx;
}

.tag-name {
  font-size: 40rpx;
  font-weight: 500;
  color: #000;
}

.tag-select-r-list {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: left;
}

.tag-select-r {
  padding: 15rpx 40rpx !important;
  margin: 20rpx 30rpx 20rpx 0rpx;
  font-size: 32rpx;
  font-size: 500;
  color: #000;
  text-align: center;
  background-color: #f2f2f2;
  border: 1rpx solid transparent;
  border-radius: 10rpx;
}

.page_flex_img {
  position: absolute;
  top: 0rpx;
  right: 0rpx;
  z-index: 10001;
  width: 340rpx;
  height: 300rpx;
  background-image: url('@/resumeRelated/img/tc.png');
  background-repeat: no-repeat;
  background-position: 100% 100%;
  background-size: 100% 100%;
}

.content_list_left_color {
  margin-bottom: -10rpx;
  font-size: 28rpx;
  font-weight: 600;
  line-height: 44rpx;
  color: #000000;
}

.content_list {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;

  .content_list_left {
    position: relative;
    // width: 80%;
    display: flex;
    flex-direction: row;
    align-items: center;
    width: calc(100% - 100rpx);
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

    ._img {
      position: absolute;
      right: 10rpx;
      width: 30rpx;
      height: 30rpx;
    }

    .content_list_left_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      // width: 100%;
      padding: 10rpx 20rpx;
      white-space: nowrap;

      .content_list_for {
        display: inline-block;
        margin: auto;

        .content_list_border_1 {
          padding-left: 30rpx;
        }
      }
    }
  }
}

.select_border {
  width: 100%;
  padding: 11rpx 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  line-height: 44rpx;
  color: #333333;
  text-align: center;
  background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
  border-radius: 16rpx 16rpx 16rpx 16rpx;
}

.content_flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0rpx 40rpx 0rpx;
  padding-bottom: 0;

  .content_search {
    display: flex;
    flex-direction: row;
    width: 100%;

    .content_search_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      padding: 20rpx 30rpx;
      background: #ffffff;
      border-radius: 20rpx 20rpx 20rpx 20rpx;
      box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

      .content_search_left {
        display: flex;
        align-items: center;
        width: 10%;

        ._image {
          width: 40rpx;
          height: 40rpx;
        }
      }

      .content_search_right {
        width: 90%;
        padding-left: 10rpx;
      }
    }
  }
}
</style>
