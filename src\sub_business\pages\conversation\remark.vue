<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '备注',
    navigationBarBackgroundColor: '#ffffff',
  },
}
</route>

<template>
  <z-paging :paging-style="pageStyle" layout-only safe-area-inset-bottom bottom-bg-color="#ffffff">
    <template #top>
      <view class="px94rpx pt80rpx">
        <wd-input
          v-model="remarkModel"
          clear-trigger="focus"
          clearable
          placeholder="请输入备注内容"
          custom-class="!text-center"
        />
      </view>
    </template>
    <template #bottom>
      <view class="px84rpx pb70rpx">
        <wd-button custom-class="w-full !h-84rpx !bg-#075EFF" @click="handleSetRemark">
          <text class="c-#FFFFFF text-24rpx font500">设置</text>
        </wd-button>
      </view>
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import { CommonUtil, useToast } from 'wot-design-uni'

const toast = useToast()
const { pageParams } = usePagePeriod<{ conversationId: string }>()
const { pageStyle } = usePaging({
  style: {
    background: '#ffffff',
  },
})
const remarkModel = ref('')
const handleSetRemark = CommonUtil.debounce(() => {
  if (!remarkModel.value.trim()) {
    toast.show('请输入备注内容')
    return
  }
  uni.$UIKit.contactStore.setContactRemark(pageParams.value.conversationId, remarkModel.value)
}, 300)
</script>

<style lang="scss" scoped>
//
</style>
