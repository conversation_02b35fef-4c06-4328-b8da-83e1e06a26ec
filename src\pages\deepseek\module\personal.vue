<template>
  <view class="content">
    <z-paging
      ref="pagingRef"
      v-model="dataList"
      use-chat-record-mode
      safe-area-inset-bottom
      bottom-bg-color="#f8f8f8"
      empty-view-text="有什么可以帮忙的？"
      @query="queryList"
      @keyboardHeightChange="keyboardHeightChange"
      @hidedKeyboard="hidedKeyboard"
      :paging-style="pageStyle"
      empty-view-img="/static/img/deepSeek.png"
      chat-adjust-position-offset="-10px"
    >
      <template #top>
        <CustomNavBar>
          <template #left>
            <view class=""></view>
          </template>
          <template #content>
            <wd-img width="226rpx" height="52rpx" :src="deepseekTitle" class="m-t-10rpx"></wd-img>
          </template>
          <!-- <template #right>
            <image class="tabbarImg" src="/static/img/histryIcon.png" @click="show = true"></image>
          </template> -->
        </CustomNavBar>
      </template>

      <view v-for="(item, index) in dataList" :key="index" style="position: relative">
        <view style="transform: scaleY(-1)">
          <chat-item :item="item" :headImg="headImg"></chat-item>
        </view>
      </view>
      <template #bottom>
        <chat-input-bar :disabled="isAnswering" ref="inputBar" @send="doSend" />
        <customTabbar name="deepseek" />
      </template>
    </z-paging>
    <gao-ChatSSEClient
      ref="chatSSEClientRef"
      @onOpen="openCore"
      @onError="errorCore"
      @onMessage="messageCore"
      @onFinish="finishCore"
    />
  </view>
</template>
<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { onLoad, onHide, onUnload } from '@dcloudio/uni-app'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import customTabbar from '@/components/common/custom-tabbar.vue'
import collentPopup from '@/components/deepseek/person/collent.vue'
import chatInputBar from '@/components/chat-input-bar/chat-input-bar.vue'
import chatItem from '@/components/chat-item/chat-item.vue'
import deepseekTitle from '@/static/img/deepseekTitle.png'
import { formatDateTime } from '@/utils/common'
import { historyList, chat } from '@/interPost/deepSeek'
import { usePaging } from '@/hooks/common/usePaging'

defineOptions({
  name: 'DeepSeekPersonal',
})
// 弹窗
const show = ref(false)
const handleClose = () => {
  show.value = false
}
// 图片上传
const headImg = ref('')
// sse hook
const dataList = ref([])
const { userIntel } = useUserInfo()
const {
  chatSSEClientRef,
  start: sseStart,
  stop: sseStop,
  openCore,
  messageCore: sseMessageCore,
} = useDeepseeksse()
// 初始化分页
const { pagingRef, pageInfo, pageData, pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

// 响应式数据
const params = ref({
  entity: {},
  orderBy: {},
  page: pageInfo.page,
  size: pageInfo.size,
})
const inputBar = ref(null)
const askMsg = ref('')
const isAnswering = ref(false)

// 查询历史记录
const queryList = async () => {
  const res: any = await historyList(params.value)
  pagingRef.value.complete(res.data.list)
}

// 生命周期
onLoad(async () => {
  await uni.$onLaunched
  await nextTick()
  if (!userIntel.value?.headImgUrl) {
    headImg.value =
      userIntel.value.gender === 1
        ? '/static/header/jobhunting1.png'
        : '/static/header/jobhunting2.png'
  } else {
    headImg.value = userIntel.value?.headImgUrl
  }
  pagingRef.value.reload()
})

// 键盘事件处理
const keyboardHeightChange = (res) => {
  inputBar.value.updateKeyboardHeightChange(res)
}

const hidedKeyboard = () => {
  inputBar.value.hidedKeyboard()
}
const errorCore = () => {
  isAnswering.value = false
}
// 完成
const finishCore = () => {
  isAnswering.value = false
}
// 封装 messageCore，保留 dataList 逻辑
const messageCore = (msg) => {
  sseMessageCore(msg)
  // 过滤掉 [DONE] 消息
  if (msg.data !== '[DONE]') {
    // 如果第一条消息是"思考中..."，则替换为实际内容
    if (dataList.value[0] && dataList.value[0].content === '思考中...') {
      if (msg.data && msg.data !== '') {
        dataList.value[0].content = msg.data
      }
    } else {
      dataList.value[0].content += `${msg.data.replace(/(?:\*){1,2}/g, '')}`
    }
  }
}

// 发送消息
const doSend = (msg) => {
  if (isAnswering.value && (!msg.trim().length || msg.trim() === '')) return
  askMsg.value = msg
  pagingRef.value.addChatRecordData({
    content: msg,
    icon: headImg.value,
    createTime: formatDateTime(),
    isMe: true,
    type: 0,
  })
  doAnswer()
}
// 获取AI回答
const doAnswer = async () => {
  isAnswering.value = true
  try {
    // 添加"思考中"提示
    pagingRef.value.addChatRecordData({
      createTime: formatDateTime(),
      icon: '/static/img/deepseek2.png',
      content: '思考中...',
      isMe: false,
      type: 1,
    })
    // 获取API响应
    sseStart('/easyzhipin-ai/aliChat/chat', askMsg.value)
  } catch (error) {
    if (dataList.value.length > 0) {
      dataList.value[0].content = '回答时出错，请稍后再试'
    }
  }
}

// 隐藏
onHide(() => {
  sseStop()
})
// 卸载
onUnload(() => {
  sseStop()
})
</script>
<style scoped lang="scss">
.tabbarImg {
  width: 40rpx;
  height: 40rpx;
}
.header {
  padding: 20rpx;
  font-size: 20rpx;
  color: white;
  background-color: red;
}
.popup {
  position: absolute;
  top: -20px;
  z-index: 1000;
  width: 400rpx;
  height: 200rpx;
  background-color: red;
}
.start_photo {
  box-sizing: 220rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-bottom: 200rpx;
  transform: rotate(180deg);
}
.start_deep {
  width: 340rpx;
  height: 340rpx;
}
</style>
