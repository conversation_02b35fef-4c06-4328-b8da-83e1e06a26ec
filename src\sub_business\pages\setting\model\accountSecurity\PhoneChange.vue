<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging :paging-style="pageStyle" layout-only>
    <template #top>
      <CustomNavBar title="修改手机号"></CustomNavBar>
    </template>
    <view class="setting">
      <!-- 旧手机号码 -->
      <view class="setting-list border-b">
        <view class="list-item-text text-32rpx">旧手机号码</view>
        <view class="input-container">
          <wd-input v-model="fromData.phone" disabled no-border placeholder="请输入旧手机号码" />
        </view>
      </view>
      <view class="setting-list border-b">
        <view class="list-item-text text-32rpx">验证码</view>
        <view class="input-container">
          <wd-input
            v-model="fromData.captchaVerifyParam"
            :maxlength="4"
            no-border
            placeholder="请输入旧手机号验证码"
          />
          <view
            :class="{ 'code-btn-disabled': oldPhoneCountdown > 0 }"
            class="code-btn"
            @click="getOldCode"
          >
            {{ oldPhoneCountdown > 0 ? `${oldPhoneCountdown}s` : '获取验证码' }}
          </view>
        </view>
      </view>
      <!-- 新手机号码 -->
      <view class="setting-list border-b">
        <view class="list-item-text text-32rpx">新手机号码</view>
        <view class="input-container">
          <wd-input
            v-model="fromData.phoneTwo"
            :maxlength="11"
            no-border
            placeholder="请输入新手机号码"
          />
        </view>
      </view>
      <!-- 验证码 -->
      <view class="setting-list border-b">
        <view class="list-item-text text-32rpx">验证码</view>
        <view class="input-container">
          <wd-input
            v-model="fromData.captchaVerifyParamTwo"
            :maxlength="4"
            no-border
            placeholder="请输入新手机号验证码"
          />
          <view
            :class="{ 'code-btn-disabled': newPhoneCountdown > 0 }"
            class="code-btn"
            @click="getNewCode"
          >
            {{ newPhoneCountdown > 0 ? `${newPhoneCountdown}s` : '获取验证码' }}
          </view>
        </view>
      </view>
    </view>
  </z-paging>

  <!-- 底部固定按钮 -->
  <view class="btn-fixed" @click="confirmChange">
    <view class="btn_box">
      <view class="btn_bg">确认修改</view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { useUserStore } from '@/store'
import { sendOldPhoneCode, sendUpdatePhoneCode, updateMyPhone } from '@/interPost/my'
import { maskPhoneNumber } from '@/utils/common'

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

const fromData = ref({
  phone: '',
  phoneTwo: '',
  captchaVerifyParam: '',
  captchaVerifyParamTwo: '',
})

// 获取用户store
const userStore = useUserStore()

const oldPhoneCode = ref<any>('')
const newPhoneCode = ref<any>('')

// 倒计时状态
const oldPhoneCountdown = ref(0)
const newPhoneCountdown = ref(0)

// 倒计时定时器
let oldPhoneTimer: NodeJS.Timeout | null = null
let newPhoneTimer: NodeJS.Timeout | null = null

// 计算脱敏后的手机号
const originalPhone = computed(() => {
  const phone = userStore.userInfo?.phone
  if (phone) {
    return maskPhoneNumber(phone)
  }
  return '未设置'
})

// 开始倒计时
const startCountdown = (type: 'old' | 'new') => {
  const countdown = type === 'old' ? oldPhoneCountdown : newPhoneCountdown
  const timer = type === 'old' ? oldPhoneTimer : newPhoneTimer
  countdown.value = 60
  const interval = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(interval)
      if (type === 'old') {
        oldPhoneTimer = null
      } else {
        newPhoneTimer = null
      }
    }
  }, 1000)
  if (type === 'old') {
    oldPhoneTimer = interval
  } else {
    newPhoneTimer = interval
  }
}

// 获取验证码
const getOldCode = async () => {
  const res: any = await sendOldPhoneCode()
  if (res.code === 0) {
    uni.showToast({
      title: '验证码发送成功',
      icon: 'success',
      duration: 2000,
    })
    // 开始倒计时
    startCountdown('old')
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'error',
      duration: 2000,
    })
  }
}

const getNewCode = async () => {
  // 如果正在倒计时，不允许点击
  if (newPhoneCountdown.value > 0) {
    return
  }

  if (fromData.value.phoneTwo === '') {
    uni.showToast({
      title: '请输入新手机号码',
      icon: 'none',
      duration: 2000,
    })
    return
  }
  const res: any = await sendUpdatePhoneCode({
    phone: fromData.value.phoneTwo,
  })
  if (res.code === 0) {
    uni.showToast({
      title: '验证码发送成功',
      icon: 'success',
      duration: 2000,
    })
    // 开始倒计时
    startCountdown('new')
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'error',
      duration: 2000,
    })
  }
}

// 确认修改
const confirmChange = async () => {
  if (fromData.value.phone === '') {
    uni.showToast({
      title: '请输入旧手机号码',
      icon: 'none',
      duration: 2000,
    })
    return
  }
  if (fromData.value.phoneTwo === '') {
    uni.showToast({
      title: '请输入新手机号码',
      icon: 'none',
      duration: 2000,
    })
    return
  }
  if (fromData.value.captchaVerifyParam === '') {
    uni.showToast({
      title: '请输入旧手机验证码',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  if (fromData.value.captchaVerifyParamTwo === '') {
    uni.showToast({
      title: '请输入新手机验证码',
      icon: 'none',
      duration: 2000,
    })
    return
  }
  const res: any = await updateMyPhone({
    captchaVerifyParam: fromData.value.captchaVerifyParam,
    phoneTwo: fromData.value.phoneTwo,
    captchaVerifyParamTwo: fromData.value.captchaVerifyParamTwo,
  })
  if (res.code === 0) {
    uni.showToast({
      title: '修改成功，请重新登录',
      icon: 'none',
      duration: 2000,
    })

    // 清空用户信息
    userStore.clearUserInfo()

    // 延迟跳转，让用户看到提示信息
    setTimeout(() => {
      // 跳转到登录页面，并清除所有页面栈
      uni.reLaunch({
        url: '/pages/login/index',
      })
    }, 2000)
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 2000,
    })
  }
}

onShow(() => {
  fromData.value.phone = originalPhone.value
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (oldPhoneTimer) {
    clearInterval(oldPhoneTimer)
  }
  if (newPhoneTimer) {
    clearInterval(newPhoneTimer)
  }
})
</script>
<style lang="scss" scoped>
:deep(.wd-input) {
  width: 100%;
  text-align: left;
  background-color: transparent;
}

:deep(.wd-input__placeholder) {
  font-size: 28rpx !important;
  color: #888888;
}

:deep(.wd-picker__placeholder) {
  font-size: 28rpx !important;
  color: #888888;
}

:deep(.wd-input__inner) {
  font-size: 32rpx !important;
  font-weight: 500;
}

:deep(.uni-input-input) {
  font-size: 32rpx !important;
  font-weight: normal !important;
  color: #333333 !important;
}

:deep(.wd-input__placeholder) {
  font-size: 28rpx !important;
  font-weight: normal !important;
}

.setting {
  padding: 0rpx 40rpx;
  padding-bottom: 200rpx; // 为底部按钮留出空间

  .setting-list {
    padding: 30rpx 20rpx 30rpx 20rpx;

    .list-item-text {
      margin-bottom: 20rpx;
      color: #333;
    }

    .input-container {
      display: flex;
      gap: 20rpx;
      align-items: center;

      .wd-input {
        flex: 1;
      }

      .code-btn {
        padding: 12rpx 24rpx;
        margin-right: -20rpx;
        font-size: 28rpx;
        color: #333;
        white-space: nowrap;
        cursor: pointer;
        border-radius: 8rpx;
        transition: all 0.3s ease;

        &.code-btn-disabled {
          display: flex;
          align-items: center;
          justify-content: center;
          min-width: 100rpx;
          height: 52rpx;
          color: #999999;
          cursor: not-allowed;
          background: #cccccc;
        }
      }
    }
  }

  .row-main {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .phone-arrow {
    display: flex;
    align-items: center;

    .phone-text {
      margin-right: 10rpx;
      font-size: 28rpx;
      color: #888;
    }
  }

  .tip-text {
    margin-top: 8rpx;
    margin-left: 2rpx;
    font-size: 22rpx;
    line-height: 1.4;
    color: #999999;
  }
}

// 底部固定按钮样式
.btn-fixed {
  position: fixed;
  right: 0;
  bottom: 48rpx;
  left: 0;
  z-index: 10;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  padding: 0rpx 40rpx;

  .btn_box {
    box-sizing: border-box;
    width: 100%;

    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 32rpx;
      font-weight: normal;
      color: #333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
</style>
