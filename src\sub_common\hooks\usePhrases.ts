import { storeToRefs } from 'pinia'
import { usePhrasesCommonStore } from '@/store'
import type { sysUserCommonPhraseQueryListInt } from '@/service/sysUserCommonPhrase/types'

const phrasesCommonActive = ref<Partial<sysUserCommonPhraseQueryListInt>>({})
const phrasesCommonList = ref<sysUserCommonPhraseQueryListInt[]>([])
/** 常用语hooks */
export const usePhrases = () => {
  const { userIntel } = useUserInfo()
  /** 招呼语信息 */
  const phrasesGreetInfo = computed(() => {
    const { phrasesGreetDefault } = storeToRefs(usePhrasesCommonStore())
    return phrasesGreetDefault.value[userIntel.value.type] || []
  })

  /**
   * 合并常用语数据：优先使用用户自定义数据，补充系统默认数据
   * @param list 用户自定义常用语列表
   * @returns 合并后的常用语列表，按 sortNo 排序
   */
  function phrasesCommonFilterInfo(
    list: sysUserCommonPhraseQueryListInt[] = [],
  ): sysUserCommonPhraseQueryListInt[] {
    const defaultCommon = phrasesGreetInfo.value || []
    if (defaultCommon.length === 0) {
      return list.sort((a, b) => (a.sortNo || 0) - (b.sortNo || 0))
    }
    const userPhraseMap = new Map<string, sysUserCommonPhraseQueryListInt>()
    const userCustomPhrases: sysUserCommonPhraseQueryListInt[] = []
    list.forEach((item) => {
      if (item.systemDefaultKey) {
        userPhraseMap.set(item.systemDefaultKey, item)
      } else {
        userCustomPhrases.push(item)
      }
    })
    const mergedData: sysUserCommonPhraseQueryListInt[] = []
    defaultCommon.forEach((defaultItem) => {
      const userOverride = userPhraseMap.get(defaultItem.systemDefaultKey)
      mergedData.push(userOverride || defaultItem)
    })
    mergedData.push(...userCustomPhrases)
    return mergedData.sort((a, b) => (a.sortNo || 0) - (b.sortNo || 0))
  }
  const setPhrasesCommonActive = (phrase: Partial<sysUserCommonPhraseQueryListInt>) => {
    phrasesCommonActive.value = phrase
  }
  const clearPhrasesCommonActive = () => {
    phrasesCommonActive.value = {}
  }

  /**
   * 删除本地数据（用于删除系统默认的常用语项）
   * @param systemDefaultKey 系统默认键值
   */
  const deleteLocalPhraseData = (systemDefaultKey: string) => {
    const { phrasesGreetDefault } = storeToRefs(usePhrasesCommonStore())
    if (!systemDefaultKey) {
      console.warn('deleteLocalPhraseData: systemDefaultKey 不能为空')
      return
    }
    const currentUserType = userIntel.value.type
    const currentData = phrasesGreetDefault.value[currentUserType] || []

    phrasesGreetDefault.value[currentUserType] = currentData.filter(
      (item) => item.systemDefaultKey !== systemDefaultKey,
    )
  }

  onUnmounted(() => {
    clearPhrasesCommonActive()
  })
  return {
    phrasesCommonFilterInfo,
    phrasesCommonList,
    phrasesGreetInfo,
    setPhrasesCommonActive,
    phrasesCommonActive,
    deleteLocalPhraseData,
  }
}
