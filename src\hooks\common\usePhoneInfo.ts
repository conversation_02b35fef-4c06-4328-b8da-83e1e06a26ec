export const usePhoneVersion = () => {
  // 获取版本信息
  const getAppVersion = (): Promise<{ versionName: string; versionCode: string }> => {
    return new Promise((resolve) => {
      uni.getSystemInfo({
        success: (res) => {
          console.log('res:', res)
          resolve({
            versionName: res.appVersion || '',
            versionCode: res.appVersionCode || '',
          })
        },
        fail: (err) => {
          console.error('获取版本信息失败:', err)
          resolve({
            versionName: '',
            versionCode: '',
          })
        },
      })
    })
  }

  const getPhoneInfo = (): Promise<UniApp.GetSystemInfoResult> => {
    return new Promise((resolve) => {
      uni.getSystemInfo({
        success: (res) => {
          resolve(res)
        },
      })
    })
  }
  const callPhone = (phone: string) => {
    console.log('phone:', phone)
    uni.makePhoneCall({
      phoneNumber: phone,
    })
  }
  return {
    getAppVersion,
    getPhoneInfo,
    callPhone,
  }
}
