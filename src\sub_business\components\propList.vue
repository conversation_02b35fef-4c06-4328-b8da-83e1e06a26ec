<template>
  <view
    v-for="(item, index) in enumPropList"
    :key="`prop-${index}`"
    :style="{
      backgroundColor: item.backgroundColor,
      maxHeight: expandedKey !== index ? '112rpx' : '960rpx',
      transition: 'max-height 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    }"
    class="w-full rounded-56rpx cursor-pointer overflow-hidden relative"
    @click="toggleExpand(index)"
  >
    <view
      class="absolute bottom--3px z-10 left-50% transform-translate-x--50% w-46rpx h-20rpx bg-#EBEFFA rounded-t-full"
    ></view>
    <view
      class="absolute top--3px z-10 left-50% transform-translate-x--50% w-46rpx h-20rpx bg-#EBEFFA rounded-b-full"
    ></view>
    <view v-if="expandedKey === index" class="absolute top-0 right-0 z-1">
      <view class="relative">
        <wd-img
          :height="item.rightInfo.height"
          :src="item.rightInfo.src"
          :width="item.rightInfo.width"
        />
        <view
          v-if="item.key === 'UNLIMITED_CHAT'"
          class="absolute top-28rpx right-28rpx c-#FFCECE text-20rpx center"
        >
          {{ payPropInfo[`propId${item.value}EndTime`] }}
        </view>
      </view>
    </view>
    <view class="flex items-center h-112rpx px-38rpx">
      <view
        :class="expandedKey === index ? 'items-end' : 'items-center'"
        :style="{
          transition: 'all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        }"
        class="flex w-full gap-12rpx"
      >
        <view class="center size-64rpx bg-#ffffff rounded-full">
          <wd-icon :name="item.icon" size="40rpx" />
        </view>
        <view
          :style="{
            transform: expandedKey === index ? 'translateX(0)' : 'translateX(calc(100% - 202rpx))',
            transition: 'transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
          }"
          class="flex flex-1 items-center"
        >
          <view class="flex-1">
            <wd-img :src="item.title" height="26rpx" width="202rpx" />
          </view>
          <text
            v-if="
              expandedKey === index &&
              item.key !== 'UNLIMITED_CHAT' &&
              payPropInfo[`propId${item.value}Count`]
            "
            class="c-white text-28rpx"
          >
            {{ `使用中（${payPropInfo[`propId${item.value}Count`]}）` }}
          </text>
        </view>
      </view>
    </view>
    <view
      :style="{
        opacity: expandedKey === index ? '1' : '0',
        transform:
          expandedKey === index ? 'scale(1) translateY(0)' : 'scale(0.95) translateY(-10rpx)',
        transition:
          expandedKey === index
            ? 'all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) 0.1s'
            : 'all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
      }"
      class="transform-gpu mt--14rpx"
      @click.stop="handleLookPropDetail(item)"
    >
      <view class="px-38rpx pb-48rpx flex flex-col gap-30rpx">
        <view class="flex flex-col gap-30rpx">
          <view>
            <image :src="item.titleUs" class="h-28rpx" mode="heightFix" />
          </view>
          <text class="c-white text-26rpx whitespace-pre-wrap">
            {{ payPropInfo[`propId${item.value}Desc`] ?? '' }}
          </text>
        </view>
        <view v-if="item.key !== 'UNLIMITED_CHAT'" class="flex items-center">
          <text class="flex-1 c-white text-24rpx font500">Value Easy Coin</text>
          <view v-if="showPayButton" @click.stop="handlePayProp(item)">
            <wd-button
              :round="false"
              custom-class="!rounded-20rpx !bg-#ffffff w-170rpx !p-0"
              size="large"
            >
              <text :style="{ color: item.backgroundColor }" class="font-bold text-24rpx">
                立即购买
              </text>
            </wd-button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { enumProp } from '@/sub_business/enum'
import { usePayProp } from '@/sub_business/hooks/usePayProp'

interface propsInt {
  propType?: number
  showPayButton?: boolean
}

const props = withDefaults(defineProps<propsInt>(), {
  showPayButton: true,
})

const { payPropInfo, payPropActive } = usePayProp()
const expandedKey = ref<number | null>(0)
const propsPropType = computed(() => props.propType)
const enumPropList = computed(() => {
  if (propsPropType.value) {
    const filteredProp = enumProp.find((item) => item.value === propsPropType.value)
    if (filteredProp) {
      return [filteredProp]
    }
  }
  return enumProp
})

const toggleExpand = (index: number) => {
  expandedKey.value = expandedKey.value === index ? null : index
}

function handleLookPropDetail(item: (typeof enumProp)[number]) {
  if (propsPropType.value !== undefined && propsPropType.value !== null) {
    toggleExpand(expandedKey.value)
    return
  }
  payPropActive.value = item.value
  uni.navigateTo({
    url: `/sub_business/pages/prop/details`,
  })
}

function handlePayProp(item: (typeof enumProp)[number]) {
  payPropActive.value = item.value
  uni.navigateTo({
    url: `/sub_business/pages/prop/pay`,
  })
}
</script>

<style lang="scss" scoped>
//
</style>
