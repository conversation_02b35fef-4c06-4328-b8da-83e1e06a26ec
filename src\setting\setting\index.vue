<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging :paging-style="pageStyle" layout-only>
    <template #top>
      <CustomNavBar title="设置"></CustomNavBar>
    </template>
    <view class="setting">
      <view class="setting-list flex-between" @click="goAccountNumber">
        <view class="list-item-text text-32rpx">账号与安全中心</view>
        <wd-icon
          class="arrow-right-icon"
          color="#888888"
          name="chevron-right"
          size="20px"
        ></wd-icon>
      </view>
      <view class="divider"></view>
      <view class="setting-list flex-between" @click="goNotice">
        <view class="list-item-text text-32rpx">通知与提醒</view>
        <wd-icon
          class="arrow-right-icon"
          color="#888888"
          name="chevron-right"
          size="20px"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between" @click="goPrivacyProtection">
        <view class="list-item-text text-32rpx">隐私保护</view>
        <wd-icon
          class="arrow-right-icon"
          color="#888888"
          name="chevron-right"
          size="20px"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between" @click="goGeneralSetup">
        <view class="list-item-text text-32rpx">通用设置</view>
        <wd-icon
          class="arrow-right-icon"
          color="#888888"
          name="chevron-right"
          size="20px"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between" @click="goInfoCollection">
        <view class="list-item-text text-32rpx">信息收集清单</view>
        <wd-icon
          class="arrow-right-icon"
          color="#888888"
          name="chevron-right"
          size="20px"
        ></wd-icon>
      </view>
      <view class="divider"></view>
      <view class="setting-list flex-between" @click="goAbout">
        <view class="list-item-text text-32rpx">关于易直聘</view>
        <wd-icon
          class="arrow-right-icon"
          color="#888888"
          name="chevron-right"
          size="20px"
        ></wd-icon>
      </view>
      <view class="divider"></view>
      <view class="setting-list flex-between">
        <view class="list-item-text text-32rpx">版本更新</view>
        <view class="subText flex-c">
          <view v-if="versionInfo?.versionName" class="p-r-10rpxrpx">
            V{{ versionInfo.versionName }}
          </view>
          <wd-icon
            class="arrow-right-icon"
            color="#888888"
            name="chevron-right"
            size="20px"
          ></wd-icon>
        </view>
      </view>
      <view class="setting-list flex-between" @click="chanangeIdentity">
        <view class="list-item-text text-32rpx">切换身份</view>
        <view class="subText flex-c">
          <view class="p-r-10rpxrpx">{{ userRoleIsBusiness ? '伯乐' : '黑马' }}</view>
          <wd-icon
            class="arrow-right-icon"
            color="#888888"
            name="chevron-right"
            size="20px"
          ></wd-icon>
        </view>
      </view>

      <view class="btn-flexd">
        <view class="btn-flexd-red" @click="logoutBtm">退出登录</view>
      </view>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

const { userRoleIsBusiness } = useUserInfo()
const { getAppVersion } = usePhoneVersion()
const { logoutBtn } = useLogout()
const versionInfo = ref<{ versionName?: string; versionCode?: string }>({})
onLoad(async () => {
  versionInfo.value = await getAppVersion()
})
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
// 退出登录
const logoutBtm = async () => {
  logoutBtn()
}

// 切换身份
const chanangeIdentity = () => {
  uni.navigateTo({
    url: '/setting/IdentitySwitching/index',
  })
}
const goNotice = () => {
  uni.navigateTo({
    url: '/setting/notice/index',
  })
}
const goPrivacyProtection = () => {
  uni.navigateTo({
    url: '/setting/PrivacyAgreement/index',
  })
}
const goAccountNumber = () => {
  uni.navigateTo({
    url: '/setting/accountNumber/index',
  })
}

// 个人信息清单
const goInfoCollection = () => {
  uni.navigateTo({
    url: '/setting/InfoCollection/index',
  })
}

// 关于易直聘
const goAbout = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/aboutUs/index',
  })
}

const goGeneralSetup = () => {
  uni.navigateTo({
    url: '/setting/generalSettings/index',
  })
}
</script>
<style lang="scss" scoped>
.setting {
  padding: 0rpx 40rpx;
  // margin-bottom: 320rpx;
  .setting-list {
    padding: 20rpx 20rpx;
    margin: 16rpx 0;

    .list-item-text {
      color: #333;
    }

    // 移除border-b相关样式
  }
}

.divider {
  height: 1rpx;
  margin: 0 20rpx;
  background: #d7d6d6;
}

.btn-flexd-red {
  height: 80rpx;
  line-height: 80rpx;
  color: #fff;
  text-align: center;
  background-color: #ff8080;
  border-radius: 30rpx;
}

.btn-flexd {
  position: fixed;
  bottom: 40rpx;
  left: 10%;
  width: 80%;
}
</style>
