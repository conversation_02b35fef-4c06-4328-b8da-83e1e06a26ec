<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar title="账户与安全中心"></CustomNavBar>
    <view class="setting">
      <!--      <view class="setting-list flex-between" @click="goRuleCenter">-->
      <!--        <view class="list-item-text text-32rpx">规则中心</view>-->
      <!--        <wd-icon-->
      <!--          class="arrow-right-icon"-->
      <!--          color="#888888"-->
      <!--          name="chevron-right"-->
      <!--          size="20px"-->
      <!--        ></wd-icon>-->
      <!--      </view>-->
      <view class="setting-list flex-between" @click="goAccountMange">
        <view class="list-item-text text-32rpx">账号安全</view>
        <wd-icon
          class="arrow-right-icon"
          color="#888888"
          name="chevron-right"
          size="20px"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between" @click="goPermission">
        <view class="list-item-text text-32rpx">权限管理</view>
        <wd-icon
          class="arrow-right-icon"
          color="#888888"
          name="chevron-right"
          size="20px"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between" @click="goDeleteAccount">
        <view class="list-item-text text-32rpx">注销账号</view>
        <wd-icon
          class="arrow-right-icon"
          color="#888888"
          name="chevron-right"
          size="20px"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between" @click="goLoginDevice">
        <view class="list-item-text text-32rpx">登录账号管理</view>
        <wd-icon
          class="arrow-right-icon"
          color="#888888"
          name="chevron-right"
          size="20px"
        ></wd-icon>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

const goRuleCenter = () => {
  uni.navigateTo({
    url: '/setting/PrivacyAgreement/index',
  })
}
const goAccountMange = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/accountSecurity/index',
  })
}
const goLoginDevice = () => {
  uni.navigateTo({
    url: '/setting/loginDevice/index',
  })
}
// 注销账号
const goDeleteAccount = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/deregisterAccount/index',
  })
}
const goPermission = () => {
  uni.navigateTo({
    url: '/setting/permission/index',
  })
}
</script>
<style lang="scss" scoped>
.setting {
  padding: 0rpx 40rpx;

  .setting-list {
    padding: 30rpx 20rpx;

    .list-item-text {
      color: #333;
    }
  }
}

::v-deep .cell-set .u-line {
  border-bottom: 2rpx solid #d7d6d6 !important;
}

::v-deep .u-cell__title-text {
  font-size: 32rpx !important;
  font-weight: 500;
  color: #333;
}

::v-deep .u-cell__body--large {
  padding: 40rpx 0rpx;
}

::v-deep .u-cell__value {
  font-size: 28rpx;
  color: #888888;
}

::v-deep .u-cell--clickable {
  background-color: transparent !important;
}
</style>
