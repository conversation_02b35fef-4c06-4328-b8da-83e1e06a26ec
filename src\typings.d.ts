declare namespace Api {
  namespace Common {
    type EnableStatus = 0 | 1
    enum USER_TYPE {
      /** 应聘者 */
      APPLICANT = 0,
      /** HR */
      HR = 1,
    }
    /** 性别枚举 */
    enum Gender {
      /** 未设置或保密 */
      UNSET = 0,
      /** 男 */
      MALE = 1,
      /** 女 */
      FEMALE = 2,
    }
    type Record<T = any> = {
      hxUserInfoVO: {
        id: number
        nickname: string
        userId: number
        username: string
      }
    } & T
  }
  namespace Request {
    type CommonRecord<T = any> = {
      /** 主键id */
      id: number
      /** 创建时间 */
      createTime: string
      /** 创建人id */
      createUserId: number
      /** 创建人名称 */
      createUserName: string
      /** 修改时间 */
      updateTime: string
      /** 修改人id */
      updateUserId: number
      /** 修改人名称 */
      updateUserName: string
      /** 状态0不可用1正常 */
      status: Common.EnableStatus
    } & T
    type IResData = {
      code: number
      msg: string
    }
    type IResOrdinaryData<T> = IResData & {
      data: T
    }
    type IResPagingData<T> = IResData & {
      list: T[]
      total: number
    }
    type IResPagingDataParamsInt<D = AnyObject, B = AnyObject, T = AnyObject> = {
      /** 查询条件 */
      entity?: D
      /** 排序 */
      orderBy?: B
      /** 页码 */
      page: number
      /** 每页条数 */
      size: number
    } & T
  }
  namespace User {
    type IUserInfo = {
      /** 账号 */
      account: string
      /** 企业id */
      companyId: number
      /** ip */
      ip: string
      /** 是否实名认证(姓名+身份证号验证)0未认证1已认证 */
      isAuth: Common.EnableStatus
      /** 登录时间 */
      loginTime: number
      /** 登录的身份0应聘者1HR , */
      lastLoginType: Common.USER_TYPE
      /** 手机号 */
      phone: string
      /** 状态 0:禁用1:正常 */
      status: Common.EnableStatus
      /** token */
      token: string
      /** 昵称  */
      trueName: string
      /** 0应聘者1招聘者  */
      type: Common.USER_TYPE
      /** 性别 */
      gender: Common.Gender
      /** 用户(HR或应聘者)ID , */
      userId: number
      /** 用户主表id  */
      userMainId: number
      /** 是否完成必填项 */
      requiredFinishStatus: Common.EnableStatus
      /** 头像 */
      headImgUrl?: string
      /** 审核状态 */
      examineState?: number | string | null
      // hr审核状态
      hrexamineStateObj?: {
        auditStatus: number | string | null
        status: number | string | null
      }
      /** 是否已同意隐私协议 */
      hasShownAgreement: boolean
    }
  }
  namespace IM {
    /** 企业用户用户扩展信息 */
    type UserBusinessExtInfo = {
      /** 公司id */
      companyId?: number
      /** 公司名称 */
      companyName?: string
      /** 公司简称 */
      companyShortName?: string
      /** HR岗位 */
      hrPosition?: string
      /** HR用户id */
      hrUserId?: number
      /** c端用户id */
      cUserId?: number
    }
    /** 普通用户扩展信息 */
    type UserPersonalExtInfo = {
      /** C端用户id */
      cUserId?: number
    }
    /** 环信用户信息 */
    type EaseMobUserInfo = {
      /** 用户名 */
      username: string
      /** 昵称 */
      nickname: string
      /** 用户id */
      userId: number
      /** id */
      id: number
    }
    namespace CustomMessage {
      /** 自定义消息类型 */
      type ExtType =
        | 'resume'
        | 'exchange_phone'
        | 'exchange_wechat'
        | 'uninterested'
        | 'interview_appointment'
        | 'conversation_mark_sync'
      /** 0 请求 1 同意 2 拒绝 */
      type StatusType = 0 | 1 | 2
      type ExtInfo = {
        /** 消息类型 */
        type: ExtType
        /** 消息状态 */
        status: StatusType
        /** 简历交换id */
        exchange_resume_record_id?: number
        /** 手机号交换id */
        exchange_phone_record_id?: number
        /** 微信号交换id */
        exchange_wechat_record_id?: number
        /** 面试预约id */
        interview_appointment_record_id?: number
        /** 面试时间 */
        interviewTime?: string
        /** C端手机号 */
        cUserPhone?: string
        /** C端微信号 */
        cUserWechat?: string
        /** C端简历链接 */
        cUserResumeLink?: string
        /** B端手机号 */
        bUserPhone?: string
        /** B端微信号 */
        bUserWechat?: string
        /** 标记值 */
        mark?: number
        /** 同步时间 */
        syncTime?: number
        /** 来源消息类型 */
        sourceMessageType?: ExtType
      }
      /** 操作自定义消息 */
      type ModifyCustomMessage<T = any> = {
        /** 记录id */
        id: number
        /** 状态 */
        status: Exclude<StatusType, 0>
      } & T
    }
  }
}
