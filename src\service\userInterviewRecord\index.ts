import { POST } from '../index'
import { userInterviewRecordDealDataInt, userInterviewRecordDataInt } from './types'
import { HttpRequestConfig } from 'luch-request'

/** 用户处理面试邀约(接收/拒绝) */
export const userInterviewRecordDeal = (
  data: Api.IM.CustomMessage.ModifyCustomMessage<userInterviewRecordDealDataInt>,
  config?: HttpRequestConfig,
) => POST('/easyzhipin-api/userInterviewRecord/deal', data, config)

// 面试记录
export const queryTimeList = (data: userInterviewRecordDataInt, config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/hrInterviewRecord/queryTimeList', data, config)
