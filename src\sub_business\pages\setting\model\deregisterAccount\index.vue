<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar title="注销账号"></CustomNavBar>
    <view class="resignation-wrap">
      <image
        class="resignation-img"
        mode="widthFix"
        src="../../../../static/setting/bg-warning.png"
      />

      <view class="content-text">
        <span class="content-text-item">
          尊敬的用户，当您选择注销易直聘App账号，将产生以下后果：
        </span>
        <span class="content-text-item">
          1.账号将无法登录，不能再使用本平台任何服务。后续即便用相同手机号重新注册，也无法找回已注销账号添加或绑定的任何内容；
        </span>
        <span class="content-text-item">
          2.账号内的个人资料（如昵称、头像、联系方式）、求职或招聘历史信息（投递记录、聊天记录、收藏职位等）都将被删除或匿名化处理（依法需留存的除外），后续无法检索和恢复；
        </span>
        <span class="content-text-item">
          3.若账号内有未使用完的虚拟资产、付费服务权益，将视作您自动放弃 。
        </span>
        <span class="content-text-item mt-60rpx">
          注销是不可逆操作，请您谨慎考虑，确认无未完成事项后再进行。
        </span>
      </view>

      <view class="btn-fixed">
        <view class="btn-wrap">
          <view class="btn_box">
            <view class="btn_bg" @click="showVerificationModal">确认注销</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 验证码弹框 -->
    <view v-if="showModal" class="modal-overlay" @click="closeModal">
      <view class="modal-container" @click.stop>
        <view class="modal-content">
          <view class="modal-header">
            <text class="modal-title">注销账号</text>
          </view>
          <view class="modal-body">
            <view class="input-group">
              <input
                v-model="verificationCode"
                :maxlength="4"
                class="verification-input"
                no-border
                placeholder="请输入验证码"
                type="text"
              />
              <view class="get-code-btn" @click="getVerificationCode">
                {{ countdown > 0 ? `${countdown}s` : '获取验证码' }}
              </view>
            </view>
          </view>
        </view>

        <!-- 确认注销按钮 - 在模态框外部 -->
        <view class="modal-footer-external">
          <view class="confirm-btn-external" @click="confirmDeregister">确认注销</view>
        </view>
      </view>
    </view>

    <!-- 自定义 Toast 提示 -->
    <view v-if="showToast" class="custom-toast-overlay">
      <view class="custom-toast-content">
        {{ toastMessage }}
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { useMessage } from 'wot-design-uni'
import { cancelSmsSend, cancelAccount } from '@/interPost/login'

// 弹框状态
const showModal = ref(false)
const verificationCode = ref('')
const countdown = ref(0)
// 倒计时定时器
let verificationTimer: number | null = null
const message = useMessage()

// Toast 状态
const showToast = ref(false)
const toastMessage = ref('')

// 显示验证码弹框
const showVerificationModal = () => {
  message
    .confirm({
      title: '提示',
      msg: '是否确认注销账号?',
    })
    .then(() => {
      // 延迟打开验证码弹框，确保确认弹框完全关闭
      setTimeout(() => {
        showModal.value = true
        verificationCode.value = ''
      }, 300)
    })
}

// 关闭弹框
const closeModal = () => {
  showModal.value = false
  verificationCode.value = ''
}

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60
  const interval = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(interval)
      verificationTimer = null
    }
  }, 1000)
  verificationTimer = interval
}
// 自定义 Toast 提示
const showCustomToast = (message: any) => {
  toastMessage.value = message
  showToast.value = true

  // 2秒后隐藏
  setTimeout(() => {
    showToast.value = false
  }, 2000)
}

// 获取验证码
const getVerificationCode = async () => {
  if (countdown.value > 0) return

  const res: any = await cancelSmsSend()
  if (res.code === 0) {
    verificationTimer = null
    // 开始倒计时
    startCountdown()
  } else {
    showCustomToast(res?.msg || '获取验证码失败')
  }
}

// 确认注销
const confirmDeregister = async () => {
  if (!verificationCode.value) {
    showCustomToast('请输入验证码')
    return
  }

  const res: any = await cancelAccount({ code: verificationCode.value, deviceInfo: {} })
  if (res.code === 0) {
    showCustomToast('账号注销成功')
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/login/index',
      })
    }, 2000)
  } else {
    showCustomToast(res?.msg || '注销失败')
  }
}

// 组件卸载时清理定时器
onUnmounted(() => {
  if (verificationTimer) {
    clearInterval(verificationTimer)
  }
})
</script>

<style lang="scss" scoped>
.resignation-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 110rpx;

  .resignation-img {
    width: 200rpx;
    height: 200rpx;
  }

  .resignation-text {
    margin-top: 30rpx;
    font-size: 32rpx;
    color: #000000;
  }

  .resignation-text-sub {
    margin-top: 10rpx;
    font-size: 24rpx;
    color: #333333;
  }

  .content-text {
    width: 85%;
    padding: 20rpx 0;
    margin-top: 80rpx;
    font-size: 28rpx;
    color: #333333;
    // border: 1px solid #333333;
    border-radius: 14rpx 14rpx 0 0;

    .content-text-item {
      display: block;
      margin-bottom: 10rpx;
    }
  }

  .btn-fixed {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 999;
    padding: 20rpx 40rpx;
  }

  .btn-wrap {
    display: flex;
    flex-direction: column;
    gap: 20rpx; // 按钮间距
  }

  .btn_box {
    width: 100%;
  }

  .btn_box_3 {
    margin-top: -10rpx;
  }

  .btn_bg {
    width: 100%;
    padding: 20rpx 0;
    font-size: 32rpx;
    color: #333;
    text-align: center;
    background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
    border-radius: 14px;
  }

  .btn_bg_1 {
    background: linear-gradient(90deg, #ffc2c2 0%, #ff5151 100%);
  }

  .not_leave {
    width: 100%;
    padding: 0 0 20rpx 0;
    font-size: 32rpx;
    color: #333;
    text-align: center;
    border-radius: 14px;
  }
}

// 弹框样式
.modal-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.modal-content {
  width: 600rpx;
  overflow: hidden;
  background-color: #fff;
  border-radius: 20rpx;
}

.modal-header {
  padding: 40rpx 40rpx 20rpx;
  text-align: center;

  .modal-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }
}

.modal-body {
  padding: 20rpx 40rpx 48rpx;

  .input-group {
    display: flex;
    gap: 20rpx;
    align-items: center;

    .verification-input {
      flex: 1;
      height: 80rpx;
      padding: 0 20rpx;
      font-size: 28rpx;

      &:focus {
        background-color: #fff;
        border-color: #007aff;
      }
    }

    .get-code-btn {
      padding: 20rpx 30rpx;
      font-size: 28rpx;
      color: #333333;
      white-space: nowrap;
      border-radius: 10rpx;
    }
  }
}

.modal-footer {
  padding: 0 40rpx 40rpx;

  .confirm-btn {
    width: 100%;
    height: 96rpx;
    font-size: 32rpx;
    line-height: 96rpx;
    color: #fff;
    text-align: center;
    background: linear-gradient(90deg, #ffc2c2 0%, #ff5151 100%);
    border-radius: 10rpx;

    &:active {
      opacity: 0.8;
    }
  }
}

.modal-footer-external {
  width: 600rpx;
  margin-top: 20rpx;

  .confirm-btn-external {
    width: 100%;
    height: 80rpx;
    font-size: 32rpx;
    line-height: 80rpx;
    color: #ff5151;
    text-align: center;
    background: #ffffff;
    border-radius: 10rpx;

    &:active {
      opacity: 0.8;
    }
  }
}

// 自定义 Toast 样式
.custom-toast-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.custom-toast-content {
  max-width: 80%;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  color: #fff;
  text-align: center;
  word-wrap: break-word;
  pointer-events: none;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 10rpx;
}
</style>
