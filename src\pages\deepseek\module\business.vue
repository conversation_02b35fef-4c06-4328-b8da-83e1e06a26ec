<template>
  <z-paging layout-only :paging-style="pageStyle" paging-class="business-paging">
    <template #top>
      <view
        class="h-462rpx relative px-30rpx bg-cover bg-center bg-no-repeat z--1"
        :style="{ backgroundImage: `url(${releasePost})` }"
      >
        <view
          class="absolute bottom-102rpx flex items-center h-76rpx w-296rpx rounded-38rpx bg-#2F2F2F px-36rpx"
          @tap="toggleSearch"
        >
          <view class="flex items-center gap-16rpx flex-1">
            <wd-img :src="releasePostSearch" width="42rpx" height="42rpx" />
            <text class="c-#E4CC9C text-28rpx">筛选条件</text>
          </view>
          <text
            class="text-16rpx c-#E4CC9C"
            :class="
              seniorSearchVisible ? 'i-carbon-triangle-solid' : 'i-carbon-triangle-down-solid'
            "
          />
        </view>
      </view>
    </template>
    <view class="relative h-full overflow-hidden">
      <view
        class="transition-transform duration-300 ease-in-out h-full"
        :style="{ transform: `translateY(${seniorSearchVisible ? '100%' : '0'})` }"
      >
        <component :is="seniorPost" ref="seniorPostRef" />
      </view>
      <view
        class="absolute top-0 left-0 size-full transition-transform duration-300 ease-in-out"
        :style="{ transform: `translateY(${seniorSearchVisible ? '0' : '-100%'})` }"
      >
        <component
          :is="seniorSearch"
          v-model:show="selectPostPopupShowBool"
          @search="handleSeniorSearch"
          @select-post="handleSelectPost"
        />
      </view>
    </view>
    <template #bottom v-if="!seniorSearchVisible">
      <customTabbar name="deepseek" />
    </template>
  </z-paging>
  <select-post v-model:show="selectPostPopupShowBool" @select-post="handleSelectReleasePost" />
</template>

<script lang="ts" setup>
import { useReleasePost } from '@/hooks/business/useReleasePost'
import { hrPositionQueryOptionList } from '@/service/hrPosition'
import type { hrResumeSeniorPostModelInt } from '@/service/hrResume/types'
import type { hrPositionQueryOptionListInt } from '@/service/hrPosition/types'
import customTabbar from '@/components/common/custom-tabbar.vue'
import seniorPost from '@/components/deepseek/business/senior-post.vue'
import seniorSearch from '@/components/deepseek/business/senior-search.vue'
import selectPost from '@/components/common/select-post.vue'
import releasePost from '@/static/deepseek/business/release-post.png'
import releasePostSearch from '@/static/deepseek/business/release-post-search.png'

defineOptions({
  name: 'DeepSeekBusiness',
})
const { releaseSeniorPostActivePost } = useReleasePost()
const { pageStyle } = usePaging({
  style: {
    background: '#383838',
  },
})
const {
  bool: seniorSearchVisible,
  setFalse: seniorSearchVisibleFalse,
  setTrue: seniorSearchVisibleTrue,
} = useBoolean()
const {
  bool: selectPostPopupShowBool,
  setTrue: selectPostPopupShowBoolTrue,
  setFalse: selectPostPopupShowBoolFalse,
} = useBoolean()
const seniorPostRef = ref<InstanceType<typeof seniorPost>>()
const toggleSearch = async () => {
  if (seniorSearchVisible.value) {
    seniorSearchVisibleFalse()
  } else {
    seniorSearchVisibleTrue()
  }
}
async function fetchHrPositionQueryOptionList() {
  const { data } = await hrPositionQueryOptionList({
    page: 1,
    size: 1,
  })
  const { list } = data
  const [first] = list
  refreshHomeList(first)
}
async function refreshHomeList(post: hrPositionQueryOptionListInt) {
  releaseSeniorPostActivePost.value = post ?? {}
  seniorPostRef.value.reload()
}
function handleSeniorSearch(model: hrResumeSeniorPostModelInt) {
  seniorSearchVisibleFalse()
  seniorPostRef.value.seniorPostReload(model)
}
async function handleSelectPost() {
  selectPostPopupShowBoolTrue()
}
function handleSelectReleasePost(post: hrPositionQueryOptionListInt) {
  refreshHomeList(post)
  selectPostPopupShowBoolFalse()
  toggleSearch()
}
onMounted(async () => {
  await uni.$onLaunched
  fetchHrPositionQueryOptionList()
})
</script>

<style lang="scss" scoped>
:deep(.wd-navbar__left) {
  align-items: end;
}

.business-paging {
  :deep(.zp-view-super) {
    margin: -60rpx 0 0;
  }
  :deep(.zp-paging-container-content) {
    height: 100%;
  }
}
</style>
