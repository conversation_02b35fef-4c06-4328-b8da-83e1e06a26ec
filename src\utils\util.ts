/** 数组去重 */
export const arrayRemoveRepeat = <T>(ary: T[]): T[] => {
  return ary.filter((item, key) => ary.indexOf(item) === key)
}

// 随机整数，大于等于80，小于等于99
export const randomInt = (min: number, max: number) => {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

// 文本截断函数 - 超过指定字数显示省略号
export const truncateText = (text: string, maxLength: number = 6): string => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

/** 字符串按指定分隔符分割转数组 */
export const splitToArray = (str: string, separator: string = ',') => {
  if (!str) return []
  return str.split(separator).filter(Boolean)
}
/**
 * 将数字转换为k格式（大于1000的转为k）
 * @param num 需要转换的数字
 * @returns 格式化后的字符串
 */
export const formatToKilo = (num: number) => {
  return num >= 1000 ? `${~~(num / 100) / 10}k` : num
}
/** 工作经验文本 */
export const workExperienceText = (start: number, end: number) => {
  if (!start && !end) return '不限'
  const validValues = Array.from(new Set([start, end].filter(Boolean)))
  return `${validValues.join('-')}年工作经验`
}

// 距离处理
export const distanceHandle = (distance: number | string) => {
  const distanceNum = Number(distance) / 1000
  const distanceStr = Math.floor(distanceNum)
  console.log(distanceStr, 'distanceStr==========')
  if (distanceStr) {
    if (distanceStr === 0) {
      return '<1km'
    } else {
      return distanceStr + 'km'
    }
  }
}

/**
 * 格式化薪资信息
 * @param workSalaryBegin 薪资范围-最低
 * @param workSalaryEnd 薪资范围-最高
 * @param salaryMonths 薪资月数
 * @param unit 薪资单位，默认为空字符串（不显示单位）
 * @returns 格式化后的薪资字符串
 */
export const formatSalary = (
  workSalaryBegin: number,
  workSalaryEnd: number,
  salaryMonths: number = 12,
  unit: string = '',
) => {
  const salaryRange =
    [formatToKilo(workSalaryBegin), formatToKilo(workSalaryEnd)].filter(Boolean).join('-') || '面议'
  const salaryMonthsText = salaryMonths > 12 ? `${salaryMonths}薪` : ''

  // 如果是面议，不显示单位；如果有具体薪资且配置了单位，则显示单位
  const unitText = salaryRange !== '面议' && unit ? `/${unit}` : ''

  return [salaryRange + unitText, salaryMonthsText].filter(Boolean).join(' · ')
}

// 当前时间到传入时间yyyy-mm-dd hh:mm:ss转为时间戳
export const getTimeDifference = (dateTimeStr: string): number => {
  try {
    // 解析传入的时间字符串 yyyy-mm-dd hh:mm:ss
    const targetDate = new Date(dateTimeStr.replace(/-/g, '/'))

    // 获取当前时间
    const currentDate = new Date()

    // 计算时间差（毫秒）
    const timeDifference = targetDate.getTime() - currentDate.getTime()

    return timeDifference
  } catch (error) {
    console.error('时间格式解析错误:', error)
    return 0
  }
}
