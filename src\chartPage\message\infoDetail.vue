<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging :paging-style="pageStyle" layout-only>
    <template #top>
      <CustomNavBar :title="isShow === 'sys' ? '系统消息详情' : '活动消息详情'"></CustomNavBar>
    </template>
    <view class="contanner-page">
      <view class="header-row">
        <view class="text-28rpx font-w-500 c-#333 flex-1">
          {{ infoObj.title }}
        </view>
        <view class="c-#555 text-24rpx">
          {{ formatDate(infoObj.createAdminTime) }}
        </view>
      </view>
      <view class="line-12 text-24rpx c-#555 mt-28rpx">
        {{ infoObj.content }}
      </view>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { queryDetailById, queryActivitiListById } from '@/interPost/messege'

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
// id
const id = ref(null)
const isShow = ref(null)
const infoObj = ref<{
  title?: string
  createAdminTime?: string
  content?: string
}>({})
// 获取列表
// 格式化日期，只显示年月日
const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}年${month}月${day}日`
}

const queryList = async () => {
  if (isShow.value === 'sys') {
    const res: any = await queryDetailById({ id: id.value })
    if (res.code === 0) {
      infoObj.value = res.data
    }
  } else {
    const res: any = await queryActivitiListById({ id: id.value, messageType: 1 })
    if (res.code === 0) {
      infoObj.value = res.data
    }
  }
}
onLoad(async (options) => {
  await uni.$onLaunched
  id.value = options.id
  isShow.value = options.isShow
  queryList()
})
</script>

<style lang="scss" scoped>
.contanner-page {
  padding: 20rpx 20rpx 40rpx;
  margin: 40rpx;
  background: #fff;
  border-radius: 10rpx;
  box-shadow: 8rpx 8rpx 33rpx 0px rgba(0, 0, 0, 0.1);
}

.header-row {
  display: flex;
  gap: 20rpx;
  align-items: center;
  justify-content: space-between;
  margin-right: 28rpx;
}
</style>
