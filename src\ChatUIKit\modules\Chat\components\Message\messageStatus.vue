<template>
  <view :class="`status-${msg.status}`" class="msg-status-wrap">
    <view v-if="msg.status === 'sending'">
      <text class="c-gray-500 text-24rpx">发送中</text>
    </view>
    <view v-else-if="msg.status === 'sent'">
      <text class="c-gray-500 text-24rpx">已发送</text>
    </view>
    <view v-else-if="msg.status === 'failed'">
      <text class="c-red-500 text-24rpx">发送失败</text>
    </view>
    <view v-else-if="msg.status === 'received'">
      <text class="c-gray-500 text-24rpx">送达</text>
    </view>
    <view v-else-if="msg.status === 'read'">
      <text class="c-gray-500 text-24rpx">已读</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { MixedMessageBody } from '../../../../types'

interface Props {
  msg: MixedMessageBody
}

defineProps<Props>()
</script>

<style lang="scss" scoped>
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.msg-status-wrap {
  position: absolute;
  display: inline-block;
  bottom: 0px;

  &.status-sending {
    left: -45px; // 发送中 3个字
  }

  &.status-sent {
    left: -45px; // 已发送 3个字
  }

  &.status-failed {
    left: -55px; // 发送失败 4个字
  }

  &.status-received {
    left: -35px; // 送达 3个字
  }

  &.status-read {
    left: -35px; // 已读 2个字
  }
}

.msg-status {
  width: 20px;
  height: 20px;
}

.sending {
  background-position: center center;
  background-image: url('../../../../assets/icon/spinner.png');
  background-size: 100%;
  animation: spin 1s linear infinite;
  background-repeat: no-repeat;
}

.sent {
  background-position: center center;
  background-image: url('../../../../assets/icon/check.png');
  background-size: 100%;
  background-repeat: no-repeat;
}

.failed {
  background-position: center center;
  background-image: url('../../../../assets/icon/failed.png');
  background-size: 100%;
  background-repeat: no-repeat;
}

.received {
  background-position: center center;
  background-image: url('../../../../assets/icon/received.png');
  background-size: 100%;
  background-repeat: no-repeat;
}

.read {
  background-position: center center;
  background-image: url('../../../../assets/icon/read.png');
  background-size: 100%;
  background-repeat: no-repeat;
}

@import url('../../../../styles/common.scss');
</style>
