import { hrPositionAddDataInt } from '@/service/hrPosition/types'
import { ReleaseStep } from '@/sub_business/types/release'
import { CommonUtil } from 'wot-design-uni'

const defaultReleasePostModel: hrPositionAddDataInt = {
  companyWorkAddressId: null,
  jobType: null,
  positionBenefitList: [],
  positionKeyList: [],
  positionCode: null,
  positionDesc: null,
  positionName: null,
  workEducational: 0,
  workExperience: 0,
  workSalary: [],
  workExperienceEnd: null,
  workExperienceStart: null,
  workSalaryBegin: null,
  workSalaryEnd: null,
  positionMarkName: '',
  positionMarkCode: null,
  salaryFixed: null,
  salaryFloat: null,
  salaryMonths: null,
  salaryType: 0,
  recruitingNum: 0,
}

const releasePostModel = ref<hrPositionAddDataInt>({
  ...CommonUtil.deepClone(defaultReleasePostModel),
})
const releaseCurrentStep = ref(ReleaseStep.RELEASE)
const releasePositionMark = ref<AnyArray>([])
const releasePositionKey = ref<string | null>(null)
const releaseActiveAddress = ref<AnyObject>({})
/** 发布岗位hooks */
export const useReleasePost = () => {
  // 简化后的薪资范围生成逻辑
  const salaryRanges = {
    // 1k-10k，每个起始值对应5个连续的结束值
    ...generateSalaryRanges(1, 10, 5),
    // 11k-50k，每个起始值对应10个连续的结束值
    ...generateSalaryRanges(11, 50, 10),
  }

  /** 生成薪资范围数据 */
  function generateSalaryRanges(start: number, end: number, rangeSize: number) {
    const result: Record<string, string[]> = {}
    for (let i = start; i <= end; i++) {
      const key = `${i}k`
      result[key] = []
      for (let j = 1; j <= rangeSize; j++) {
        if (i + j <= 60) {
          result[key].push(`${i + j}k`)
        }
      }
    }
    return result
  }

  /** 重置发布岗位数据 */
  const resetReleasePostModel = () => {
    releasePostModel.value = CommonUtil.deepClone(defaultReleasePostModel)
    releaseCurrentStep.value = ReleaseStep.RELEASE
    releasePositionKey.value = ''
    releasePositionMark.value = []
  }
  const releaseSubmitPostModel = computed(() => {
    const { workSalary, workExperience, ...params } = releasePostModel.value
    return params
  })
  return {
    releasePostModel,
    releaseSalaryColumns: salaryRanges,
    releaseSubmitPostModel,
    releasePositionKey,
    releasePositionMark,
    releaseCurrentStep,
    resetReleasePostModel,
    releaseActiveAddress,
  }
}
