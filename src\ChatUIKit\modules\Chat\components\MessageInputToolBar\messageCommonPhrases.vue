<template>
  <view class="flex flex-col gap-10rpx px-40rpx bg-#ffffff">
    <view class="flex items-stretch pb-10rpx">
      <text class="c-#333333 text-28rpx font500 flex-1">常用语</text>
      <view class="center gap-6rpx" @tap="handleOperatePhrases('edit', 'chat')">
        <wd-icon :name="phrasesAdd" size="36rpx" />
        <text class="c-#333333 text-24rpx">管理</text>
      </view>
    </view>
    <view class="flex items-center flex-row flex-wrap h-340rpx overflow-y-scroll">
      <view
        v-for="(item, key) in phrasesCommonList"
        :key="key"
        class="py-30rpx border-b-1px border-b-solid border-b-[#E8E8E8] w-full flex items-center gap-28rpx"
      >
        <text class="c-#555555 text-24rpx line-clamp-1 flex-1" @click="selectPhrase(item.content)">
          {{ item.content }}
        </text>
        <wd-button custom-class="!min-w-112rpx !h-52rpx" plain @click="sendPhrase(item.content)">
          <text class="text-24rpx">发送</text>
        </wd-button>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { CommonUtil } from 'wot-design-uni'
import { EMIT_EVENT } from '@/enum'
import { sysUserCommonPhraseQueryList } from '@/service/sysUserCommonPhrase'
import { usePhrases } from '@/sub_common/hooks/usePhrases'
import phrasesAdd from '@/ChatUIKit/static/message-tool-bar/phrases-add.png'

const emits = defineEmits(['sendPhrase', 'selectPhrase'])

const { phrasesCommonFilterInfo, phrasesCommonList } = usePhrases()

async function fetchSysUserCommonPhraseQueryList() {
  const { data } = await sysUserCommonPhraseQueryList({
    page: 1,
    size: 20,
  })
  const { list } = data
  phrasesCommonList.value = CommonUtil.deepClone(phrasesCommonFilterInfo(list))
}

const sendPhrase = CommonUtil.debounce((phrase: string) => {
  emits('sendPhrase', phrase)
}, 300)
const selectPhrase = (phrase: string) => {
  emits('selectPhrase', phrase)
}

function handleOperatePhrases(type: 'add' | 'edit', pathType?: string) {
  const urls = {
    add: '/sub_common/pages/phrases/add',
    edit: '/sub_common/pages/phrases/index',
  }
  uni.navigateTo({
    url: CommonUtil.buildUrlWithParams(urls[type], {
      type,
      pathType,
    }),
  })
}

function reload() {
  fetchSysUserCommonPhraseQueryList()
}

uni.$on(EMIT_EVENT.REFRESH_COMMON_PHRASES, reload)
onMounted(() => {
  reload()
})
onUnmounted(() => {
  uni.$off(EMIT_EVENT.REFRESH_COMMON_PHRASES)
})
</script>

<style lang="scss" scoped></style>
