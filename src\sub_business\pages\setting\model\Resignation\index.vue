<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar title="切换公司"></CustomNavBar>
    <view class="resignation-wrap">
      <image
        class="resignation-img"
        mode="widthFix"
        src="../../../../static/setting/bg-warning.png"
      />
      <view>
        <view class="resignation-text">
          您的HR管理权限将在24小时后自动失效，相关数据将按以下规则处理：
        </view>
        <!--        <view class="resignation-text-sub">（当前公司：重庆中誉易职网络信息技术有限公司）</view>-->
      </view>

      <view class="content-text">
        <span class="content-text-item">
          1.您创建的职位信息可移交至企业管理员账号，由新负责人接管；
        </span>
        <span class="content-text-item">
          2.所有招聘数据（职位/候选人记录/沟通记录等）将被永久清空，无法恢复；
        </span>
        <span class="content-text-item">
          3.系统将保留您的基础账号信息（如手机号、邮箱），但无法再登录该企业 HR 后台。
        </span>
        <span class="content-text-item">
          若您需备份重要资料，请在24小时内登录系统完成操作。感谢您对平台的支持，祝您职业发展顺利！
        </span>
      </view>

      <view class="btn-fixed">
        <view class="btn-wrap">
          <view class="btn_box">
            <view class="btn_bg">交换至其他账号</view>
          </view>
          <view class="btn_box">
            <view class="btn_bg btn_bg_1">确认切换公司</view>
          </view>
          <!--          <view class="btn_box btn_box_3">-->
          <!--            <view class="not_leave">暂不离开</view>-->
          <!--          </view>-->
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
</script>

<style lang="scss" scoped>
.resignation-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 80rpx;

  .resignation-img {
    width: 200rpx;
    height: 200rpx;
  }

  .resignation-text {
    margin: 30rpx 48rpx 0;
    font-size: 32rpx;
    color: #000000;
    text-align: center;
  }

  .resignation-text-sub {
    margin-top: 10rpx;
    font-size: 24rpx;
    color: #333333;
    text-align: center;
  }

  .content-text {
    width: 85%;
    padding: 20rpx 0;
    margin-top: 30rpx;
    font-size: 28rpx;
    color: #333333;
    // border: 1px solid #333333;
    border-radius: 14rpx 14rpx 0 0;

    .content-text-item {
      display: block;
      margin-bottom: 10rpx;
    }
  }

  .btn-fixed {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 999;
    padding: 20rpx 40rpx;
  }

  .btn-wrap {
    display: flex;
    flex-direction: column;
    gap: 20rpx; // 按钮间距
  }

  .btn_box {
    width: 100%;
  }

  .btn_box_3 {
    margin-top: -10rpx;
  }

  .btn_bg {
    width: 100%;
    padding: 20rpx 0;
    font-size: 32rpx;
    color: #333;
    text-align: center;
    background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
    border-radius: 14px;
  }

  .btn_bg_1 {
    background: linear-gradient(90deg, #ffc2c2 0%, #ff5151 100%);
  }

  .not_leave {
    width: 100%;
    padding: 0 0 20rpx 0;
    font-size: 32rpx;
    color: #333;
    text-align: center;
    border-radius: 14px;
  }
}
</style>
