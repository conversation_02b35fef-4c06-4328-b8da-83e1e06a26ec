<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '岗位管理',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging ref="pagingRef" v-model="pageData" :paging-style="pageStyle" @query="queryList">
    <template #top>
      <CustomNavBar title="岗位管理">
        <template #left>
          <wd-icon
            class="back-button"
            color="#000000"
            name="arrow-left"
            size="20"
            @click="goBack"
          />
        </template>
      </CustomNavBar>
      <view class="page-top">
        <!-- 添加岗位 -->
        <view class="recruit-card" @click="goAddPosition">
          <view class="recruit-card-content">
            <!-- 左侧内容 -->
            <view class="items-center">
              <view class="recruit-title-row">
                <view class="recruit-title">添加岗位</view>
                <image
                  class="recruit-arrow-line"
                  mode="widthFix"
                  src="@/static/my/business/line.png"
                />
              </view>
              <view class="recruit-list">
                <text class="recruit-positions-text">
                  {{ recruitPositions }}
                </text>
              </view>
            </view>
            <!-- 右侧指标 -->
            <view class="items-center-right">
              <image
                class="recruit-arrow"
                mode="aspectFit"
                src="@/static/my/business/add_block.png"
              />
            </view>
          </view>
        </view>

        <!-- tab切换 -->
        <view class="tab-container">
          <view
            v-for="tab in tabList"
            :key="tab.value"
            :class="['tab-item', { active: activeTab === tab.value }]"
            @click="handleTabClick(tab.value)"
          >
            <text class="tab-label">{{ tab.label }}</text>
            <view v-if="activeTab === tab.value" class="tab-underline"></view>
          </view>
        </view>
      </view>
    </template>
    <view class="job-list">
      <view v-for="job in jobList" :key="job.id" class="job-card" @click="goPositionDetail(job)">
        <!-- <view
          class="job-status"
          :class="
            job.status === 1
              ? 'recruiting'
              : job.status === 2
                ? 'closed'
                : job.status === 0
                  ? 'draft'
                  : ''
          "
        >
          {{
            job.status === 1 ? '在招' : job.status === 2 ? '关闭' : job.status === 0 ? '草稿' : ''
          }}
        </view> -->
        <view class="job-header">
          <view class="job-title">
            <image
              v-if="job.isRecruit === 1"
              class="urgent-icon"
              mode="aspectFit"
              src="@/static/mine/business/urgent-recruitment.png"
            />
            <view class="job-title-text">{{ job.positionName }}</view>
          </view>
        </view>
        <view class="job-tags">
          <text v-for="tag in job.positionKey" :key="tag" class="job-tag">{{ tag }}</text>
        </view>
        <view class="job-desc">{{ job.positionDesc }}</view>
      </view>
    </view>
  </z-paging>
  <wd-toast />
  <wd-message-box />
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { queryMyPositionListByStatus } from '@/interPost/my'

defineOptions({
  name: 'HomePersonal',
})
// vuex数据
const { pagingRef, pageInfo, pageData, pageStyle, pageSetInfo } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

const params = reactive({
  entity: {
    isRecruit: null,
    status: 1,
  },
  orderBy: {},
  page: 1,
  size: 10,
})

// 招聘岗位列表
const recruitPositions = ref('添加需要招聘的岗位')
const tabList = [
  { label: '在招', value: 'recruiting' },
  { label: '关闭', value: 'closed' },
  { label: '急招', value: 'urgent' },
  { label: '草稿', value: 'draft' },
]
const activeTab = ref('recruiting')

// 岗位列表示例
const jobList = ref([])

const handleTabClick = (status) => {
  if (status === 'recruiting') {
    params.entity.status = 1
    params.entity.isRecruit = 0
  } else if (status === 'closed') {
    params.entity.status = 2
    params.entity.isRecruit = 0
  } else if (status === 'urgent') {
    params.entity.status = 1
    params.entity.isRecruit = 1
  } else if (status === 'draft') {
    params.entity.status = 0
    params.entity.isRecruit = 0
  }
  activeTab.value = status
  pagingRef.value.reload()
}

const goAddPosition = () => {
  uni.navigateTo({
    url: '/sub_business/pages/release/index',
  })
}

// 查看岗位详情
const goPositionDetail = (job) => {
  uni.navigateTo({
    url: `/resumeRelated/jobDetail/index?id=${job.id}&companyId=${job.companyId}&type=business`,
  })
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 获取我发布于的岗位列表
const queryList = async (page, size) => {
  console.log('获取列表position')
  pageSetInfo(page, size)
  const res: any = await queryMyPositionListByStatus({
    ...params,
    page: pageInfo.page,
    size: pageInfo.size,
  })
  if (res.code === 0) {
    const list = (res.data?.list || []).map((item) => {
      return {
        ...item,
        positionKey: item.positionKey?.split(',') || [],
      }
    })
    jobList.value = list
    // 完成分页数据处理，这是关键步骤
    pagingRef.value.complete(res.data?.list || [])
  } else {
    // 处理错误情况
    pagingRef.value.complete([])
  }
}

onLoad(async (options) => {
  await uni.$onLaunched
  pagingRef.value.reload()
})

// 页面显示时重新加载数据
onShow(() => {
  pagingRef.value?.reload()
})

// 监听页面间通信，当岗位状态发生变化时重新加载
onMounted(() => {
  uni.$on('positionStatusChanged', () => {
    pagingRef.value.reload()
  })
})

onUnmounted(() => {
  uni.$off('positionStatusChanged')
})
</script>

<style lang="scss" scoped>
.page-top {
  padding: 64rpx 32rpx 0 32rpx;

  .recruit-card {
    position: relative;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    width: 100%;
    height: 156rpx;
    padding: 0 32rpx;
    margin-bottom: 20rpx;
    background: url('@/static/my/business/positions_recruiting.png') no-repeat center/cover;
    border-radius: 24rpx;

    .recruit-card-content {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .items-center {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .recruit-title-row {
          display: flex;
          align-items: center;
        }

        .recruit-title {
          margin-right: 16rpx;
          font-size: 32rpx;
          font-weight: normal;
          color: #333333;
          white-space: nowrap;
        }

        .recruit-arrow-line {
          width: 280rpx;
          height: 280rpx;
          margin-right: 32rpx;
          margin-left: 32rpx;
          vertical-align: middle;
        }

        .recruit-list {
          max-width: 400rpx;
          margin-top: 22rpx;
          font-size: 26rpx;
          color: #333333;
          text-align: left;
          opacity: 0.9;

          .recruit-positions-text {
            display: inline-block;
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }

      .items-center-right {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: -64rpx;
        margin-left: -16rpx;

        .recruit-arrow {
          width: 128rpx;
          height: 128rpx;
          vertical-align: middle;
        }
      }
    }
  }
}

.tab-container {
  display: flex;
  justify-content: flex-start; // 左对齐
  margin: 20rpx 0;

  .tab-item {
    position: relative;
    padding: 20rpx 32rpx 0 0; // 右侧间距
    font-size: 30rpx;
    color: #999;
    // flex: 1;  // 删除这一行
    text-align: center;

    .tab-label {
      position: relative;
      z-index: 1;
      display: inline-block;
      font-weight: normal;
    }

    .tab-underline {
      position: absolute;
      bottom: -4rpx;
      left: 33%;
      width: max-content;
      min-width: 55rpx;
      height: 4rpx;
      padding: 0 8rpx;
      margin-top: 10rpx;
      background: #2a5fff;
      border-radius: 2rpx;
      transform: translateX(-50%);
    }

    &.active {
      font-weight: bold;
      color: #000000;

      .tab-label {
        font-weight: bold;
      }
    }
  }
}

.job-list {
  margin: 32rpx 32rpx 0 32rpx;

  .job-card {
    position: relative;
    padding: 32rpx;
    margin-bottom: 40rpx;
    background: #fff;
    border-radius: 32rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);

    .job-status {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 2;

      display: flex;
      align-items: center; // 垂直居中
      justify-content: center; // 水平居中
      max-width: 96rpx;
      height: 54rpx;
      padding: 6rpx 16rpx;
      font-size: 22rpx;
      line-height: 54rpx;
      border-radius: 0 32rpx 0 32rpx;

      &.recruiting {
        color: #fff;
        background: #506dff;
        box-shadow: none;
      }

      &.closed {
        color: #888888;
        background: #e1e1e1;
      }

      &.draft {
        color: #888888;
        background: #e1e1e1;
      }
    }

    .job-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .job-title {
        display: flex;
        align-items: center; // 垂直方向居中
        font-size: 32rpx;
        font-weight: 500;
        color: #000000;

        .urgent-icon {
          width: 64rpx;
          height: 32rpx;
          margin-right: 8rpx; // 图标和文字间距
        }

        .job-title-text {
          padding-bottom: 2rpx;
        }
      }
    }

    .job-tags {
      margin: 16rpx 0;

      .job-tag {
        display: inline-block;
        padding: 4rpx 12rpx;
        margin-right: 12rpx;
        font-size: 22rpx;
        color: #888888;
        background: #f3f3f3;
        border-radius: 8rpx;
      }
    }

    .job-desc {
      display: -webkit-box;
      margin-top: 8rpx;
      overflow: hidden;
      font-size: 28rpx;
      color: #333;
      text-overflow: ellipsis;
      word-break: break-all;
      -webkit-line-clamp: 2; // 限制为2行
      -webkit-box-orient: vertical;
    }
  }
}
</style>
