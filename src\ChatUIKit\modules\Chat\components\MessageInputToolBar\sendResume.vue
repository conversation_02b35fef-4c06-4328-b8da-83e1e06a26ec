<template>
  <view class="flex justify-center" @tap="handleSendResume">
    <ItemContainer :title="resumeTitle" :icon-url="resumeImg" />
  </view>
</template>

<script lang="ts" setup>
import { autorun } from 'mobx'
import { EMIT_EVENT } from '@/enum'
import type { InputToolbarEvent } from '@/ChatUIKit/types/index'
import ItemContainer from './itemContainer.vue'
import resumeImg from '@/ChatUIKit/static/message-custom/resume.png'

const toolbarInject = inject<InputToolbarEvent>('InputToolbarEvent')
const { userRoleIsBusiness } = useUserInfo()
const { sendCustomResumeMessage, getIMUserInfo, customCardInfo, getConversationInfo } =
  useIMConversation()
const extUserInfo = ref<Api.IM.UserBusinessExtInfo>({})

const resumeTitle = computed(() => (userRoleIsBusiness.value ? '要简历' : '发送简历'))

async function handleSendResume() {
  const { conversationId } = getConversationInfo.value
  if (!conversationId) {
    uni.showToast({
      title: '请稍后重试',
      icon: 'none',
    })
    return
  }
  const userId = userRoleIsBusiness.value ? extUserInfo.value.cUserId : extUserInfo.value.hrUserId
  try {
    await sendCustomResumeMessage(conversationId, {
      id: customCardInfo.value.positionInfoId,
      hxUserInfoVO: {
        userId,
      },
    })
    toolbarInject?.closeToolbar()
  } catch {
    if (userRoleIsBusiness.value) return
    uni.$emit(EMIT_EVENT.PROMPT_COMPLETE_RESUME)
  }
}

const unwatchUserInfo = autorun(() => {
  const { conversationId, conversationType } = getConversationInfo.value
  if (conversationType === 'singleChat') {
    const { ext } = getIMUserInfo(conversationId)
    extUserInfo.value = JSON.parse(ext || '{}') as Api.IM.UserBusinessExtInfo
  }
})
onUnmounted(() => {
  unwatchUserInfo()
})
</script>

<style lang="scss" scoped>
//
</style>
