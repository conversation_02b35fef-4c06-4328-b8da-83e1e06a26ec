<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging :paging-style="pageStyle" layout-only>
    <template #top>
      <CustomNavBar title="通用设置"></CustomNavBar>
    </template>
    <view class="setting">
      <view class="setting-list flex-between" @click="goStorageSpace">
        <view class="list-item-text text-32rpx">储存空间管理</view>
        <wd-icon
          class="arrow-right-icon"
          color="#888888"
          name="chevron-right"
          size="20px"
        ></wd-icon>
      </view>

      <view class="setting-list flex-between" @click="generalSettings">
        <view class="list-item-text text-32rpx">设置常用语/招呼语</view>
        <wd-icon
          class="arrow-right-icon"
          color="#888888"
          name="chevron-right"
          size="20px"
        ></wd-icon>
      </view>
      <view class="divider"></view>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

const { getAppVersion } = usePhoneVersion()
const versionInfo = ref<{ versionName?: string; versionCode?: string }>({})
onLoad(async () => {
  versionInfo.value = await getAppVersion()
})
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
// 储存空间
const goStorageSpace = () => {
  uni.navigateTo({
    url: '/setting/generalSetup/index',
  })
}
// 常用语/招呼语
const generalSettings = () => {
  uni.navigateTo({
    url: '/sub_common/pages/phrases/index?type=edit&pathType=setting',
  })
}
</script>
<style lang="scss" scoped>
.setting {
  padding: 0rpx 40rpx;
  // margin-bottom: 320rpx;
  .setting-list {
    padding: 20rpx 20rpx;
    margin: 16rpx 0;

    .list-item-text {
      color: #333;
    }

    // 移除border-b相关样式
  }
}

.divider {
  height: 1rpx;
  margin: 0 20rpx;
  background: #d7d6d6;
}

.btn-flexd-red {
  height: 80rpx;
  line-height: 80rpx;
  color: #fff;
  text-align: center;
  background-color: #ff8080;
  border-radius: 30rpx;
}

.btn-flexd {
  position: fixed;
  bottom: 40rpx;
  left: 10%;
  width: 80%;
}
</style>
