import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { USER_TYPE } from '@/enum'
import { currentValueOf, timeDiffValueOf, valueOfToDate } from '@/utils/days'

type UserType = Partial<Api.User.IUserInfo>
const initState: UserType = {
  token: '',
  type: USER_TYPE.APPLICANT,
  hrexamineStateObj: {
    auditStatus: null,
    status: null,
  },
}
export const useUserStore = defineStore(
  'user',
  () => {
    const hasShownAgreement = ref(false)
    const { logoutEaseMobIM } = useEaseMobIM()
    const { stopKeepAlive } = useKeepAlive()
    const { closePushNotifications } = useIMConversation()
    const userInfo = ref<UserType>({ ...initState })
    const isLoginEd = computed(() => !!userInfo.value.token)
    const setUserLoginTime = () => {
      userInfo.value.loginTime = currentValueOf()
    }
    const setUserInfo = (val: UserType) => {
      const { token } = val
      userInfo.value = {
        ...userInfo.value,
        ...val,
      }
      // userInfo.value.type = 1 // 本地测用
      token && setUserToken(token)
    }
    const setUserHasShownAgreement = (val: boolean) => {
      hasShownAgreement.value = val
    }
    const getUserHasShownAgreement = () => {
      return hasShownAgreement.value
    }
    const setUserRoleType = (type: Api.Common.USER_TYPE) => {
      userInfo.value.type = type
    }
    const setUserToken = (token?: string) => {
      userInfo.value.token = token
      setUserLoginTime()
    }
    const getUserLoginTimeDiff = () => {
      return timeDiffValueOf(valueOfToDate(userInfo.value.loginTime), 'days')
    }
    const getToken = () => {
      return userInfo.value?.token ?? ''
    }
    const getHrExamineStateObj = () => {
      return userInfo.value.hrexamineStateObj
    }
    const clearUserInfo = () => {
      // 先清理用户数据，避免页面渲染问题
      userInfo.value = { ...initState }

      // 异步处理IM相关操作，避免阻塞页面切换
      setTimeout(() => {
        logoutEaseMobIM()
        stopKeepAlive()
        closePushNotifications()
      }, 50)
    }

    const setHrExamineStateObj = (val: any) => {
      userInfo.value.hrexamineStateObj = val
    }

    return {
      userInfo,
      isLoginEd,
      setUserInfo,
      setUserRoleType,
      setUserToken,
      getToken,
      getUserLoginTimeDiff,
      clearUserInfo,
      setHrExamineStateObj,
      getHrExamineStateObj,
      setUserHasShownAgreement,
      getUserHasShownAgreement,
    }
  },
  {
    persist: true,
  },
)
