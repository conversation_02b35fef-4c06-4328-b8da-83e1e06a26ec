<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging :paging-style="pageStyle" layout-only>
    <template #top>
      <CustomNavBar title="设置"></CustomNavBar>
    </template>
    <view class="setting">
      <view class="setting-list flex-between" @click="goCommonPhrases">
        <view class="list-item-text text-32rpx">常用语设置</view>
        <wd-icon
          class="arrow-right-icon"
          color="#888888"
          name="chevron-right"
          size="20px"
        ></wd-icon>
      </view>
      <view class="divider"></view>
      <view class="setting-list flex-between">
        <view class="list-item-text text-32rpx">黑名单</view>
        <wd-icon
          class="arrow-right-icon"
          color="#888888"
          name="chevron-right"
          size="20px"
        ></wd-icon>
      </view>
      <view class="divider"></view>
      <view class="setting-list flex-between" @click="goNotInterested">
        <view class="list-item-text text-32rpx">不感兴趣</view>
        <wd-icon
          class="arrow-right-icon"
          color="#888888"
          name="chevron-right"
          size="20px"
        ></wd-icon>
      </view>
      <view class="divider"></view>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

const chatType = ref<any>('')

const goCommonPhrases = () => {
  uni.navigateTo({
    url: '/sub_common/pages/phrases/index',
  })
}

// 不感兴趣
const goNotInterested = () => {
  if (chatType.value === 'business') {
    uni.navigateTo({
      url: '/sub_business/pages/inappropriate/index',
    })
  }
}

onLoad((options) => {
  chatType.value = options.type
})
</script>
<style lang="scss" scoped>
.setting {
  padding: 20rpx 20rpx;

  .setting-list {
    padding: 30rpx 20rpx;

    .list-item-text {
      color: #333;
    }
  }
}

.divider {
  height: 1rpx;
  margin: 0 20rpx;
  background: #d7d6d6;
}
</style>
