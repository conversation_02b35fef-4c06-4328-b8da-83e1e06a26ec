<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: 'CDK兑换',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging layout-only :paging-style="pageStyle" safe-area-inset-bottom>
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          left-arrow
          :bordered="false"
          safe-area-inset-top
          custom-class="px-25rpx"
          @click-left="handleClickLeft"
        >
          <template #title>
            <text class="c-#333333 text-32rpx font500">CDK兑换</text>
          </template>
        </wd-navbar>
        <view class="mt-60rpx px-60rpx">
          <view
            class="px-44rpx bg-white rounded-20rpx overflow-hidden shadow-[0rpx_8rpx_27rpx_0rpx_rgba(0,0,0,0.15)]"
          >
            <wd-input
              type="text"
              v-model="cdkCode"
              placeholder="输入兑换码"
              placeholder-class="text-28rpx"
              no-border
            />
          </view>
        </view>
      </wd-config-provider>
    </template>
    <view class="flex flex-col px-60rpx my-40rpx">
      <propList :prop-type="propType" :show-pay-button="false" />
    </view>
    <template #bottom>
      <view class="py-20rpx px-60rpx">
        <wd-config-provider :themeVars="themeVars">
          <wd-button
            size="large"
            :round="false"
            :loading="useCdkBool"
            custom-class="!rounded-28rpx w-full !h-130rpx"
            @click="handleUseCDK"
          >
            <text class="c-#333333 text-32rpx font500">
              {{ useCdkBool ? '兑换中...' : '立即使用' }}
            </text>
          </wd-button>
        </wd-config-provider>
      </view>
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import { CommonUtil, useToast, type ConfigProviderThemeVars } from 'wot-design-uni'
import { EMIT_EVENT } from '@/enum'
import { REG_CDK_CODE } from '@/constants/reg'
import { usePayProp } from '@/sub_business/hooks/usePayProp'
import { payRedemptionCodeRedemption } from '@/service/payRedemptionCode'
import type { payRedemptionCodeRedemptionDataInt } from '@/service/payRedemptionCode/types'
import propList from '@/sub_business/components/propList.vue'

const toast = useToast()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const { pageParams } = usePagePeriod<{ params: string }>()
const { payPropActive } = usePayProp()
const { bool: useCdkBool, setFalse: setUseCdkFalse, setTrue: setUseCdkTrue } = useBoolean()
const themeVars: ConfigProviderThemeVars = {
  navbarBackground: 'transparent',
  navbarArrowSize: '40rpx',
  buttonPrimaryBgColor: 'linear-gradient( 270deg, #FFC2C2 0%, #DDDCFF 100%)',
  inputInnerHeightNoBorder: '104rpx',
  inputPlaceholderColor: '#9E9E9E',
}
const cdkCode = ref('')
const propType = computed(() => payPropActive.value || 1)
const sdkParams = computed(
  () =>
    JSON.parse(pageParams.value?.params ?? '{}') as Omit<
      payRedemptionCodeRedemptionDataInt,
      'code'
    >,
)
function handleClickLeft() {
  uni.navigateBack()
}

const handleUseCDK = CommonUtil.debounce(async () => {
  setUseCdkTrue()
  if (!REG_CDK_CODE.test(cdkCode.value)) {
    setUseCdkFalse()
    toast.show('兑换码格式不正确，请检查后重试')
    return
  }
  try {
    await payRedemptionCodeRedemption(
      {
        code: cdkCode.value,
        ...sdkParams.value,
      },
      {
        custom: {
          catch: true,
        },
      },
    )
    toast.show('兑换成功')
    setTimeout(() => {
      cdkCode.value = ''
      setUseCdkFalse()
      uni.$emit(EMIT_EVENT.REFRESH_PROP)
      uni.navigateBack({
        delta: 2,
      })
    }, 1500)
  } catch {
    setUseCdkFalse()
  }
}, 300)
</script>

<style lang="scss" scoped></style>
