<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '道具详情',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging
    ref="pagingRef"
    auto
    v-model="pageData"
    :refresher-enabled="false"
    :show-loading-more-no-more-view="false"
    :paging-style="pageStyle"
    @query="queryList"
  >
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          left-arrow
          :bordered="false"
          safe-area-inset-top
          custom-class="px-25rpx"
          @click-left="handleClickLeft"
        >
          <template #title>
            <text class="c-#333333 text-32rpx font500">我的道具</text>
          </template>
        </wd-navbar>
        <view class="flex flex-col gap-16rpx px-60rpx mt-44rpx">
          <propList :prop-type="propType" />
        </view>
      </wd-config-provider>
    </template>
    <view class="flex flex-col gap-24rpx px46rpx mt-62rpx" v-if="pageData.length">
      <text class="c-#333333 text-36rpx font500">使用中</text>
      <view class="flex flex-col gap-30rpx">
        <common-card v-for="(item, index) in pageData" :key="`prop-post-${index}`">
          <view class="p-[22rpx_34rpx] flex flex-col gap-6rpx">
            <view class="flex items-center">
              <text class="flex-1 line-clamp-1 c-#333333 text-28rpx font500">
                {{ item.positionName }}
              </text>
              <text class="c-#FF5959 text-28rpx font500">
                {{ formatSalary(item.workSalaryBegin, item.workSalaryEnd, item.salaryMonths) }}
              </text>
            </view>
            <text class="c-#9E9E9E text-22rpx">到期时间：{{ item.endTime }}</text>
          </view>
        </common-card>
      </view>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import { EMIT_EVENT } from '@/enum'
import { type ConfigProviderThemeVars } from 'wot-design-uni'
import { payPropQueryUsingPositionList } from '@/service/payProp'
import type { payPropQueryUsingPositionListInt } from '@/service/payProp/types'
import { formatSalary } from '@/utils'
import { usePayProp } from '@/sub_business/hooks/usePayProp'
import propList from '@/sub_business/components/propList.vue'
import commonCard from '@/components/common/common-card.vue'

const { pagingRef, pageStyle, pageInfo, pageData, pageSetInfo } =
  usePaging<payPropQueryUsingPositionListInt>({
    style: {
      background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
    },
  })
const { clearPayPropActive, payPropActive } = usePayProp()

const themeVars: ConfigProviderThemeVars = {
  navbarBackground: 'transparent',
  navbarArrowSize: '40rpx',
}

const propType = computed(() => payPropActive.value || 1)

async function fetchPayPropQueryUsingPositionList() {
  const { data } = await payPropQueryUsingPositionList({
    entity: {
      propId: propType.value,
    },
    ...pageInfo,
  })
  const { list, total } = data
  pagingRef.value.completeByTotal(list, total)
}
function queryList(page: number, size: number) {
  pageSetInfo(page, size)
  fetchPayPropQueryUsingPositionList()
}
function reload() {
  pagingRef.value.reload()
}
function handleClickLeft() {
  uni.navigateBack()
}
uni.$on(EMIT_EVENT.REFRESH_PROP, reload)
onBeforeUnmount(() => {
  clearPayPropActive()
  uni.$off(EMIT_EVENT.REFRESH_PROP)
})
</script>

<style lang="scss" scoped></style>
