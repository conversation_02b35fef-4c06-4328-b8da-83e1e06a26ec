import { logout } from '@/interPost/login'
import { clearStorageSync } from '@/utils/storage'
import { useLoginStore } from '@/store'

export const useLogout = () => {
  const loginStore = useLoginStore()
  const { stopKeepAlive } = useKeepAlive()
  const { clearUserInfo } = useUserInfo()
  const logoutBtn = async () => {
    const res: any = await logout()
    // 先停止保活机制，避免后台请求干扰
    stopKeepAlive()
    // 清理登录相关数据
    loginStore.sethomeJobAvtive(0)
    loginStore.sethomeCity1({})
    loginStore.sethomeCity2({})
    loginStore.sethomeCity3({})
    loginStore.setmyjobList([])
    // 清理存储数据
    clearStorageSync()
    // 最后清理用户信息和IM相关操作
    clearUserInfo()
    // 直接跳转到登录页面，避免重复跳转
    uni.reLaunch({ url: '/pages/login/index' })
  }
  return {
    logoutBtn,
  }
}
