<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging
    ref="pagingRef"
    v-model="pageData"
    @query="queryList"
    :paging-style="pageStyle"
    safe-area-inset-bottom
  >
    <template #top>
      <CustomNavBar title="首页">
        <template #left>
          <wd-icon
            name="arrow-left"
            class="back-button"
            color="#000000"
            size="20"
            @click="goBack"
          />
        </template>
      </CustomNavBar>
      <view class="page-top">
        <view class="content_flex">
          <view class="content_search">
            <view class="content_search_bg">
              <view class="content_search_left">
                <image
                  class="_image"
                  src="/static/images/home/<USER>"
                  mode="aspectFill"
                ></image>
              </view>
              <view class="content_search_right">
                <wd-input
                  clearable
                  type="text"
                  no-border
                  placeholder="搜索您想要的内容"
                  v-model="params.entity.keyword"
                  confirm-type="search"
                  @confirm="confirm"
                  @clear="clearFun"
                ></wd-input>
              </view>
            </view>
          </view>
        </view>
      </view>
    </template>
    <JobCardList v-if="pageData && pageData.length > 0" :job-list="pageData || []" />
  </z-paging>
  <wd-message-box />
</template>

<script setup lang="ts">
import { useMessage } from 'wot-design-uni'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { ChatUIKit } from '@/ChatUIKit/index'
import { useLoginStore } from '@/store'
import JobCardList from '@/components/home/<USER>/tourist.vue'
import { queryListByTourist } from '@/service/tourist'
import { numberTokw } from '@/utils/common'
import hrheader1 from '@/static/header/hrheader1.png'
import hrheader2 from '@/static/header/hrheader2.png'

const message = useMessage()
const isShow = ref(true)
const { sendGreetingMessage, sendResumeMessage } = useIMConversation()
const { getDictLabel } = useDictionary()
const appUserStore = ChatUIKit.appUserStore
const { pagingRef, pageInfo, pageData, pageStyle, pageSetInfo } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
// vuex数据
const loginStore = useLoginStore()
const params = reactive({
  orderBy: {},
  entity: {
    // 关键字
    keyword: '',
    cityCode: '',
    districtCode: '',
    provinceCode: '',
  },
})
const userToRealName = () => {
  uni.navigateTo({
    url: '/setting/tourist/index',
  })
}
// 清空
const clearFun = () => {
  params.entity.keyword = ''
  pagingRef.value.complete([])
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}
// 搜索
const confirm = () => {
  if (!params.entity.keyword) return
  pagingRef.value.reload()
}
// 获取列表
const queryList = async (page, size) => {
  pageSetInfo(page, size)
  const res: any = await queryListByTourist({
    ...params,
    size: pageInfo.size,
    page: pageInfo.page,
  })
  if (res.code === 0) {
    res.data.list.forEach((ele) => {
      // 薪资处理
      ele.hrPositionUrl = ele.sex === 1 ? hrheader1 : hrheader2
      ele.workSalaryBegin =
        ele.workSalaryBegin === 0 ? '面议' : numberTokw(ele.workSalaryBegin + '')
      ele.workSalaryEnd = ele.workSalaryEnd === 0 ? '' : numberTokw(ele.workSalaryEnd + '')
    })
    pagingRef.value.complete(res.data.list)
  }
}

onLoad(async (options) => {
  await uni.$onLaunched
  pagingRef.value.reload()
})
</script>

<style lang="scss" scoped>
.border-top {
  border-top: 2rpx dashed #d8d8d8;
}
.progress-style {
  padding: 0rpx 10rpx;
  border: 1rpx solid #ff3636;
  border-radius: 10rpx;
}

.back-button {
  position: absolute;
  left: 10px;
}

.title {
  font-size: 50rpx;
  font-weight: 500;
  color: #000000;
}

.content_flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0rpx 30rpx 0rpx;
  padding-bottom: 0;
  margin-bottom: 30rpx;

  .content_search {
    display: flex;
    flex-direction: row;
    width: 100%;

    .content_search_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      padding: 20rpx 30rpx;
      background: #ffffff;
      border-radius: 20rpx 20rpx 20rpx 20rpx;
      box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

      .content_search_left {
        display: flex;
        align-items: center;
        width: 10%;
        ._image {
          width: 40rpx;
          height: 40rpx;
        }
      }

      .content_search_right {
        width: 90%;
        padding-left: 10rpx;
      }
    }
  }
}
</style>
