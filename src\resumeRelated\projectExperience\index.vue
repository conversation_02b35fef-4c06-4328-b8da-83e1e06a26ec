<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging :paging-style="pageStyle" layout-only>
    <template #top>
      <CustomNavBar title="项目经历">
        <template #left>
          <wd-icon class="back-button" color="#000" name="arrow-left" size="20" @click="back" />
        </template>
      </CustomNavBar>
    </template>
    <view class="pageContaner">
      <view class="form-list flex-between">
        <view class="form-list-item">
          <view class="mainText p-b-10rpx">项目名称</view>
          <wd-input v-model="fromData.projectName" no-border placeholder="请输入项目名称" />
        </view>
        <view class="icon-right">
          <!-- <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon> -->
        </view>
      </view>
      <view class="form-list flex-between">
        <view class="form-list-item">
          <view class="mainText p-b-10rpx">担任角色</view>
          <wd-input v-model="fromData.takeOffice" no-border placeholder="请输入担任角色" />
        </view>
        <view class="icon-right">
          <!-- <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon> -->
        </view>
      </view>
      <view class="form-list flex-between">
        <view class="form-list-item">
          <view class="mainText p-b-10rpx">项目时间</view>
          <view class="flex-c">
            <wd-datetime-picker
              v-model="startTimeDisplay"
              :before-confirm="beforeConfirmStart"
              :default-value="defaultValue"
              :max-date="maxDate"
              :min-date="minDate"
              :zIndex="10001"
              class="flex-1"
              custom-value-class="custom-label-class"
              placeholder="开始时间"
              type="year-month"
            />
            <view class="w-20 c-#888888">至</view>
            <wd-datetime-picker
              v-model="endTimeDisplay"
              :before-confirm="beforeConfirmEnd"
              :default-value="defaultValue"
              :max-date="maxDate"
              :min-date="minDate"
              :zIndex="10001"
              class="flex-1"
              custom-value-class="custom-label-class"
              placeholder="结束时间"
              type="year-month"
            />
          </view>
        </view>
        <view class="icon-right">
          <wd-icon
            class="arrow-right-icon"
            color="#888888"
            name="chevron-right"
            size="20px"
          ></wd-icon>
        </view>
      </view>
      <view class="form-list flex-between">
        <view class="form-list-item">
          <view class="mainText p-b-10rpx">项目描述</view>
          <view
            :class="fromData.projectDescs ? 'c-#333333' : 'c-#888888'"
            class="text-28rpx text-pre-wrap single-line-ellipsis"
            @click="goWorkDescription"
          >
            {{ fromData.projectDescs ? fromData.projectDescs : '请输入项目描述' }}
          </view>
        </view>
        <view class="icon-right" @click="goWorkDescription">
          <wd-icon
            class="arrow-right-icon"
            color="#888888"
            name="chevron-right"
            size="20px"
          ></wd-icon>
        </view>
      </view>
      <view class="form-list flex-between">
        <view class="form-list-item">
          <view class="mainText p-b-10rpx">项目业绩</view>
          <view
            :class="fromData.projectPerformance ? 'c-#333333' : 'c-#888888'"
            class="text-28rpx text-pre-wrap single-line-ellipsis"
            @click="goWorkPerformance"
          >
            {{ fromData.projectPerformance ? fromData.projectPerformance : '请输入项目业绩' }}
          </view>
          <!-- <view class="color-8 text-32rpx">非必填</view> -->
        </view>
        <view class="icon-right" @click="goWorkPerformance">
          <wd-icon
            class="arrow-right-icon"
            color="#888888"
            name="chevron-right"
            size="20px"
          ></wd-icon>
        </view>
      </view>
      <view class="form-list flex-between">
        <view class="form-list-item">
          <view class="mainText p-b-10rpx">项目链接</view>
          <wd-input v-model="fromData.projectLinkAddr" no-border placeholder="请输入项目链接" />
        </view>
        <view class="icon-right">
          <!-- <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon> -->
        </view>
      </view>
    </view>
    <template #bottom>
      <view class="btn-fixed flex-c">
        <view
          v-if="isAdd === 'edit'"
          :class="isAdd === 'edit' ? 'w-30' : ''"
          class="btn-delet m-r-30rpx"
          @click="del"
        >
          删除
        </view>
        <view :class="isAdd === 'edit' ? 'w-70' : 'w-100'" class="btn_box">
          <view class="btn_bg" @click="submit">完成</view>
        </view>
      </view>
    </template>
    <wd-message-box />
  </z-paging>
</template>
<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import isEqual from 'lodash/isEqual'
import { useResumeStore } from '@/store'
import { resumeProjectAdd, resumeProjectDel, resumeProjectUpdate } from '@/interPost/resume'
import { formatTime } from '@/utils/common'
import { useMessage } from 'wot-design-uni'

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const resumeStore = useResumeStore()
const objItem = ref(null)
const isAdd = ref(null)
const message = useMessage()
const fromData = ref({
  baseInfoId: null,
  startDate: null as number | null,
  endDate: null as number | null,
  id: '',
  projectDescs: '',
  projectLinkAddr: '',
  projectName: '',
  projectPerformance: '',
  takeOffice: '',
})
// 表单初始化 - 将在数据加载完成后设置
const fromDataInit = ref({})

// 时间限制：50年前到当天
const now = new Date()
const minDate = new Date(now.getFullYear() - 50, 0, 1).getTime() // 50年前的1月1日
const maxDate = new Date().getTime() // 当天
const defaultValue = new Date().getTime() // 默认值为当前月份

// 时间显示值的计算属性（用于组件显示）
const startTimeDisplay = computed({
  get: () => {
    if (fromData.value.startDate) {
      // 如果是时间戳数字，直接转换为Date对象
      return new Date(fromData.value.startDate)
    }
    return null
  },
  set: (value: any) => {
    if (value) {
      // 处理不同类型的 value，存储为数字类型
      if (typeof value === 'number') {
        // 如果是时间戳数字
        fromData.value.startDate = value
      } else if (value instanceof Date) {
        // 如果是 Date 对象
        fromData.value.startDate = value.getTime()
      } else {
        // 其他情况，尝试转换
        fromData.value.startDate = new Date(value).getTime()
      }
    } else {
      fromData.value.startDate = null
    }
  },
})

const endTimeDisplay = computed({
  get: () => {
    if (fromData.value.endDate) {
      // 如果是时间戳数字，直接转换为Date对象
      return new Date(fromData.value.endDate)
    }
    return null
  },
  set: (value: any) => {
    if (value) {
      // 处理不同类型的 value，存储为数字类型
      if (typeof value === 'number') {
        // 如果是时间戳数字
        fromData.value.endDate = value
      } else if (value instanceof Date) {
        // 如果是 Date 对象
        fromData.value.endDate = value.getTime()
      } else {
        // 其他情况，尝试转换
        fromData.value.endDate = new Date(value).getTime()
      }
    } else {
      fromData.value.endDate = null
    }
  },
})
// 开始时间点击确认
const beforeConfirmStart = (value, resolve, picker) => {
  if (value > fromData.value.endDate && fromData.value.endDate) {
    uni.showToast({
      title: '结束时间不能小于开始时间',
      icon: 'none',
      duration: 3000,
    })
    resolve(false)
  } else {
    resolve(true)
  }
}
// 结束时间点击确认
const beforeConfirmEnd = (value, resolve, picker) => {
  if (fromData.value.startDate > value && fromData.value.startDate) {
    uni.showToast({
      title: '结束时间不能小于开始时间',
      icon: 'none',
      duration: 3000,
    })
    resolve(false)
  } else {
    resolve(true)
  }
}
onLoad(async (options) => {
  // id
  fromData.value.baseInfoId = options.id
  // 是否是新增
  isAdd.value = options.isAdd
  // 编辑的数据
  if (isAdd.value === 'edit') {
    objItem.value = JSON.parse(decodeURIComponent(options.item))
    const editData = JSON.parse(decodeURIComponent(options.item))
    fromData.value = { ...editData }

    // 如果编辑模式下的时间不是时间戳格式，需要转换为数字
    if (fromData.value.startDate) {
      if (typeof fromData.value.startDate === 'string') {
        fromData.value.startDate = new Date(fromData.value.startDate).getTime()
      }
    }
    if (fromData.value.endDate) {
      if (typeof fromData.value.endDate === 'string') {
        fromData.value.endDate = new Date(fromData.value.endDate).getTime()
      }
    }
  }

  // 在所有数据加载完成后，设置初始化数据用于比较
  await nextTick()
  fromDataInit.value = JSON.parse(JSON.stringify(fromData.value))
})
onShow(async () => {
  fromData.value.projectDescs = resumeStore.projectDescs
  fromData.value.projectPerformance = resumeStore.projectPerformance
  // 注意：这里不应该重新设置 fromDataInit，否则会导致检测不到数据变化
  // fromDataInit 只在 onLoad 时设置一次作为比较基准
})
// 项目描述
const goWorkDescription = () => {
  uni.navigateTo({
    url: '/resumeRelated/projectExperience/workContent?projectDescs=' + fromData.value.projectDescs,
  })
}
// 业绩
const goWorkPerformance = () => {
  uni.navigateTo({
    url:
      '/resumeRelated/projectExperience/workPerformance?projectPerformance=' +
      fromData.value.projectPerformance,
  })
}
// 删除
const del = async () => {
  message
    .confirm({
      title: '提示',
      msg: '您确定要删除吗?',
    })
    .then(() => {
      resumeProjectDel({ id: objItem.value.id }).then((res: any) => {
        if (res.code === 0) {
          resumeStore.setProjectDescs('')
          resumeStore.setProjectPerformance('')
          uni.navigateBack()
        } else {
          uni.showToast({
            title: res.msg,
            icon: 'none',
            duration: 3000,
          })
        }
      })
    })
}
// 返回
const back = () => {
  if (isAdd.value === 'add') {
    if (isEqual(fromData.value, fromDataInit.value)) {
      resumeStore.setProjectDescs('')
      resumeStore.setProjectPerformance('')
      uni.navigateBack()
    } else {
      message
        .confirm({
          title: '提示',
          msg: '您有内容未提交保存,确认返回吗?',
        })
        .then(() => {
          resumeStore.setProjectDescs('')
          resumeStore.setProjectPerformance('')
          uni.navigateBack()
        })
    }
  } else {
    console.log('000000')
    if (
      objItem.value.baseInfoId === fromData.value.baseInfoId &&
      objItem.value.startDate === formatTime(fromData.value.startDate) &&
      objItem.value.endDate === formatTime(fromData.value.endDate) &&
      objItem.value.id === fromData.value.id &&
      objItem.value.projectDescs === fromData.value.projectDescs &&
      objItem.value.projectLinkAddr === fromData.value.projectLinkAddr &&
      objItem.value.projectName === fromData.value.projectName &&
      objItem.value.projectPerformance === fromData.value.projectPerformance &&
      objItem.value.takeOffice === fromData.value.takeOffice
    ) {
      resumeStore.setProjectDescs('')
      resumeStore.setProjectPerformance('')
      uni.navigateBack()
    } else {
      message
        .confirm({
          title: '提示',
          msg: '您有内容未提交保存,确认返回吗?',
        })
        .then(() => {
          resumeStore.setProjectDescs('')
          resumeStore.setProjectPerformance('')
          uni.navigateBack()
        })
    }
  }
}
// 提交
const submit = async () => {
  if (isAdd.value === 'add') {
    const res: any = await resumeProjectAdd(fromData.value)
    if (res.code === 0) {
      resumeStore.setProjectDescs('')
      resumeStore.setProjectPerformance('')
      uni.navigateBack()
    } else {
      uni.showToast({
        title: res.msg,
        icon: 'none',
        duration: 3000,
      })
    }
  } else {
    const res: any = await resumeProjectUpdate(fromData.value)
    if (res.code === 0) {
      resumeStore.setProjectDescs('')
      resumeStore.setProjectPerformance('')
      uni.navigateBack()
    } else {
      uni.showToast({
        title: res.msg,
        icon: 'none',
        duration: 3000,
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .wd-input {
  width: 100%;
  text-align: left;
  background-color: transparent;
}

::v-deep .wd-input__placeholder {
  font-size: 28rpx !important;
  color: #888888;
}

::v-deep .wd-picker__placeholder {
  font-size: 28rpx !important;
  color: #888888 !important;
}

:deep(.wd-picker__value) {
  color: #333333 !important;
}

::v-deep .wd-input__inner {
  font-size: 32rpx !important;
  font-weight: 500;
}

::v-deep .wd-picker__cell {
  width: 100% !important;
  padding: 0rpx !important;
  background: transparent !important;
}

::v-deep .wd-picker__arrow {
  display: none;
}

::v-deep .uni-input-input {
  font-size: 28rpx;
  color: #333;
}

::v-deep .custom-label-class {
  view {
    font-size: 28rpx !important;
  }
}

.w-100 {
  width: 100%;
}

.btn-fixed {
  box-sizing: border-box;
  justify-content: center;
  padding: 40rpx 40rpx;

  .btn-delet {
    display: flex;
    align-items: center;
    justify-content: center;
    // width: 30%;
    padding: 20rpx 0rpx;
    font-size: 14px;
    font-weight: 500;
    color: #ffffff;
    background: #959595;
    border-radius: 14px 14px 14px 14px;
  }

  .btn_box {
    box-sizing: border-box;
    // width: 70%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}

.pageContaner {
  padding: 20rpx 40rpx 20rpx;

  .form-list {
    padding: 40rpx 0rpx;
    border-bottom: 1rpx solid #d7d6d6;

    .form-list-item {
      flex: 1;
      width: 95%;

      .single-line-ellipsis {
        display: block;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .icon-right {
      display: flex;
      justify-content: right;
      width: 100rpx;
    }
  }
}
</style>
