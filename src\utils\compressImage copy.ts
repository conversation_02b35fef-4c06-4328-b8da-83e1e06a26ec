/**
 * 图片压缩工具 - 支持H5和APP平台
 *
 * 主要功能：
 * 1. compressImage - 通用图片压缩方法
 * 2. compressForUpload - 专门用于wd-upload组件的before-upload场景
 * 3. createBeforeUploadHandler - 创建before-upload处理函数的工厂方法
 *
 * 压缩参数说明：
 * - quality: 压缩质量 (0.1-1.0)，默认0.8
 * - maxWidth: 最大宽度（像素），默认300px
 * - maxHeight: 最大高度（像素），默认300px
 * - maxSize: 最大文件大小（字节），默认20MB
 * - showLoading: 是否显示加载提示，默认true
 * - loadingText: 加载提示文字，默认'图片处理中...'
 *
 * 等比压缩说明：
 * - 系统会自动计算等比缩放比例，确保图片不变形
 * - 例如：原图 1920x1080，限制 300x300，结果：300x169
 * - 例如：原图 800x1200，限制 300x300，结果：200x300
 * - 例如：原图 200x150，限制 300x300，结果：200x150（无需缩放）
 *
 * 文件大小检查：
 * - 上传前会自动检查文件大小
 * - 超过限制会显示提示并阻止上传
 * - 默认限制：20MB
 * - 可自定义大小限制
 *
 * 注意事项：
 * 1. 质量值越低，文件越小，但图片质量越差
 * 2. 尺寸值越小，文件越小，但图片越模糊
 * 3. 建议根据实际需求平衡文件大小和图片质量
 * 4. APP端和H5端都支持等比压缩，不会导致图片变形
 * 5. APP端使用uni.compressImage + uni.getImageInfo实现等比压缩
 * 6. H5端使用Canvas实现等比压缩
 * 7. 文件大小检查在上传前进行，避免无效上传
 */

/**
 * 图片压缩方法（支持H5和APP平台）
 * @param {Object} file - 文件对象（包含url属性）
 * @param {Object} options - 压缩选项
 * @param {number} [options.quality=0.8] - 压缩质量 (0-1)
 * @param {number} [options.maxWidth=300] - 最大宽度
 * @param {number} [options.maxHeight=300] - 最大高度
 * @returns {Promise<Object>} 压缩后的文件对象
 */

interface CompressOptions {
  quality?: number
  maxWidth?: number
  maxHeight?: number
  maxSize?: number
}

interface FileObject {
  url?: string
  path?: string
  tempFilePath?: string
  filePath?: string
  src?: string
  size?: number
  raw?: File | any
  [key: string]: any
}

// 获取文件URL的辅助函数
const getFileUrl = (file: FileObject): string | null => {
  // 按优先级尝试不同的属性
  const urlSources = ['url', 'path', 'tempFilePath', 'filePath', 'src']

  for (const source of urlSources) {
    if (file[source] && typeof file[source] === 'string') {
      console.log(`使用${source}作为文件URL:`, file[source])
      return file[source]
    }
  }

  return null
}

// 使用FileReader处理本地文件的备用方法
const compressWithFileReader = (
  file: FileObject,
  options: CompressOptions,
): Promise<FileObject> => {
  return new Promise((resolve) => {
    // 如果file对象有raw属性（原始文件对象），使用它
    if (file.raw && file.raw instanceof File) {
      console.log('使用FileReader处理原始文件对象')
      const reader = new FileReader()
      reader.onload = (e) => {
        const img = new Image()
        img.onload = () => {
          try {
            const canvas = document.createElement('canvas')
            const ctx = canvas.getContext('2d')

            let width = img.width
            let height = img.height
            const { quality = 0.8, maxWidth = 300, maxHeight = 300 } = options

            if (width > maxWidth || height > maxHeight) {
              const scale = Math.min(maxWidth / width, maxHeight / height)
              width = Math.round(width * scale)
              height = Math.round(height * scale)
            }

            canvas.width = width
            canvas.height = height
            ctx.drawImage(img, 0, 0, width, height)

            const compressedDataUrl = canvas.toDataURL('image/jpeg', quality)
            resolve({
              ...file,
              url: compressedDataUrl,
              size: Math.round(compressedDataUrl.length * 0.75),
              // 添加压缩信息
              compressed: true,
              originalSize: file.size,
              compressedSize: Math.round(compressedDataUrl.length * 0.75),
              // 确保有正确的文件类型
              type: 'image/jpeg',
              name: file.name || 'compressed_image.jpg',
            })
          } catch (error) {
            console.error('FileReader压缩失败:', error)
            resolve(file)
          }
        }
        img.onerror = () => {
          console.error('FileReader图片加载失败')
          resolve(file)
        }
        img.src = e.target?.result as string
      }
      reader.onerror = () => {
        console.error('FileReader读取失败')
        resolve(file)
      }
      reader.readAsDataURL(file.raw)
    } else {
      console.log('没有找到原始文件对象，直接返回原文件')
      resolve(file)
    }
  })
}

// 处理本地文件路径
const handleLocalFilePath = (file: FileObject): Promise<FileObject> => {
  return new Promise((resolve) => {
    const fileUrl = getFileUrl(file)

    // 检查fileUrl是否存在
    if (!fileUrl) {
      console.log('文件URL不存在，直接返回原文件')
      resolve(file)
      return
    }

    // 如果是本地文件路径，尝试使用uni.getImageInfo获取信息
    if (
      !fileUrl.startsWith('http') &&
      !fileUrl.startsWith('data:') &&
      !fileUrl.startsWith('blob:')
    ) {
      console.log('检测到本地文件路径，尝试获取图片信息:', fileUrl)
      uni.getImageInfo({
        src: fileUrl,
        success: (res) => {
          console.log('获取图片信息成功:', res)
          // 这里可以进一步处理，但暂时直接返回原文件
          resolve(file)
        },
        fail: (error) => {
          console.error('获取图片信息失败:', error)
          resolve(file)
        },
      })
    } else {
      resolve(file)
    }
  })
}

export const compressImage = (file: FileObject, options: CompressOptions = {}) => {
  const { quality = 0.8, maxWidth = 300, maxHeight = 300 } = options
  console.log('开始压缩图片，参数:', { quality, maxWidth, maxHeight, originalFile: file })

  // 检查文件对象是否有效
  if (!file) {
    console.error('文件对象为空')
    return Promise.resolve(file)
  }

  // 获取文件URL
  const fileUrl = getFileUrl(file)

  // 检查文件URL是否存在
  if (!fileUrl) {
    console.error('文件URL不存在，尝试使用raw属性')
    // 如果没有url但有raw属性，尝试使用FileReader
    if (file.raw) {
      return compressWithFileReader(file, options)
    }
    console.error('文件对象无效，直接返回')
    return Promise.resolve(file)
  }

  return new Promise<FileObject>((resolve) => {
    // 获取平台信息
    const platform = uni.getSystemInfoSync().platform
    console.log('当前平台:', platform)

    // H5平台使用Canvas压缩
    if (platform === 'h5' || platform === 'windows') {
      // 首先尝试处理本地文件路径
      handleLocalFilePath(file)
        .then((processedFile) => {
          const img = new Image()

          // 处理图片URL
          const imageUrl = getFileUrl(processedFile) || ''

          // 如果是本地文件路径，转换为blob URL
          if (imageUrl.startsWith('blob:') || imageUrl.startsWith('data:')) {
            // 保持原值，不需要重新赋值
          } else if (imageUrl.startsWith('http')) {
            // 对于网络图片，尝试添加跨域支持
            img.crossOrigin = 'anonymous'
          } else {
            // 对于本地文件路径，可能需要特殊处理
            console.log('检测到本地文件路径:', imageUrl)
          }

          img.src = imageUrl
          console.log('正在加载图片:', imageUrl)

          img.onload = () => {
            try {
              console.log('原始图片尺寸:', img.width, 'x', img.height)

              // 创建Canvas元素
              const canvas = document.createElement('canvas')
              const ctx = canvas.getContext('2d')

              // 计算缩放比例
              let width = img.width
              let height = img.height
              let scale = 1

              if (width > maxWidth || height > maxHeight) {
                scale = Math.min(maxWidth / width, maxHeight / height)
                width = Math.round(width * scale)
                height = Math.round(height * scale)
                console.log('压缩后尺寸:', width, 'x', height, '缩放比例:', scale)
              } else {
                console.log('图片尺寸在限制范围内，无需缩放')
              }

              // 设置Canvas尺寸
              canvas.width = width
              canvas.height = height

              // 绘制图像
              ctx.drawImage(img, 0, 0, width, height)

              // 转换为DataURL
              const compressedDataUrl = canvas.toDataURL('image/jpeg', quality)
              console.log('压缩完成，数据URL长度:', compressedDataUrl.length)

              // 创建新的文件对象
              const newFile: FileObject = {
                ...processedFile,
                url: compressedDataUrl,
                size: Math.round(compressedDataUrl.length * 0.75), // 估算大小
                // 添加压缩信息
                compressed: true,
                originalSize: processedFile.size,
                compressedSize: Math.round(compressedDataUrl.length * 0.75),
                // 确保有正确的文件类型
                type: 'image/jpeg',
                name: processedFile.name || 'compressed_image.jpg',
              }
              console.log('压缩后的文件对象:', newFile)
              console.log('压缩效果:', {
                原始大小: processedFile.size,
                压缩后大小: Math.round(compressedDataUrl.length * 0.75),
                压缩比例: processedFile.size
                  ? Math.round((1 - (compressedDataUrl.length * 0.75) / processedFile.size) * 100) +
                    '%'
                  : '未知',
              })
              resolve(newFile)
            } catch (error) {
              console.error('H5压缩失败:', error)
              resolve(processedFile) // 失败时返回原文件
            }
          }

          img.onerror = async (error) => {
            console.error('图片加载失败:', error)
            console.error('图片URL:', imageUrl)
            console.error('文件对象:', processedFile)

            // 尝试使用备用方法：FileReader
            console.log('尝试使用FileReader备用方法')
            try {
              const result = await compressWithFileReader(processedFile, options)
              resolve(result)
            } catch (backupError) {
              console.error('备用方法也失败:', backupError)
              console.log('最终备用方案：直接返回原文件')
              resolve(processedFile)
            }
          }
        })
        .catch((error) => {
          console.error('处理本地文件路径失败:', error)
          resolve(file)
        })
    }
    // APP平台使用原生压缩
    else if (platform === 'android' || platform === 'ios') {
      console.log('使用APP原生压缩')
      compressImageInApp(file, options)
        .then(() => {
          resolve(file)
        })
        .catch((error) => {
          console.error('APP压缩失败:', error)
          resolve(file)
        })
    }
    // 其他平台直接返回
    else {
      console.log('其他平台，直接返回原文件')
      resolve(file)
    }
  })
}

/**
 * 全局图片压缩方法 - 专门用于wd-upload组件的before-upload场景
 * @param {Object} params - before-upload参数
 * @param {Array} params.files - 文件数组
 * @param {Function} params.resolve - 继续上传的回调函数
 * @param {Object} options - 压缩选项
 * @param {number} [options.quality=0.8] - 压缩质量 (0-1)
 * @param {number} [options.maxWidth=300] - 最大宽度
 * @param {number} [options.maxHeight=300] - 最大高度
 * @param {boolean} [options.showLoading=true] - 是否显示加载提示
 * @param {string} [options.loadingText='图片处理中...'] - 加载提示文字
 * @param {number} [options.maxSize=20*1024*1024] - 最大文件大小（字节），默认20MB
 * @returns {Promise<void>}
 */
export const compressForUpload = async (
  params: { files: any[]; resolve: (value: boolean) => void },
  options: CompressOptions & {
    showLoading?: boolean
    loadingText?: string
    maxSize?: number
  } = {},
) => {
  const {
    quality = 0.8,
    maxWidth = 300,
    maxHeight = 300,
    showLoading = true,
    loadingText = '图片处理中...',
    maxSize = 20 * 1024 * 1024, // 默认20MB
  } = options

  const { files, resolve } = params

  try {
    console.log('compressForUpload 开始处理:', files)

    const platform = uni.getSystemInfoSync().platform
    console.log('当前平台:', platform)

    // 处理所有文件
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      if (!file) continue

      console.log(`处理第${i + 1}个文件:`, file)

      // 检查文件大小
      if (file.size && file.size > maxSize) {
        const sizeInMB = (file.size / (1024 * 1024)).toFixed(1)
        const maxSizeInMB = (maxSize / (1024 * 1024)).toFixed(0)

        console.error(`文件过大: ${sizeInMB}MB > ${maxSizeInMB}MB`)
        uni.showToast({
          title: `图片大小为${sizeInMB}MB，超过${maxSizeInMB}MB限制，请重新选择较小的图片`,
          icon: 'none',
        })
        resolve(false)
        return
      }

      if (platform === 'android' || platform === 'ios') {
        // APP端：使用uni.compressImage压缩
        await compressImageInApp(file, { quality, maxWidth, maxHeight })
      } else {
        // H5端：使用Canvas压缩
        await compressImageInH5(file, { quality, maxWidth, maxHeight })
      }
    }

    console.log('所有文件处理完成:', files)

    // 继续上传流程
    resolve(true)
  } catch (error) {
    console.error('compressForUpload处理失败:', error)

    resolve(false)
  }
}

// APP端压缩方法
const compressImageInApp = (file: any, options: CompressOptions = {}): Promise<void> => {
  return new Promise((resolve, reject) => {
    console.log('APP端开始压缩:', file)

    const fileUrl = getFileUrl(file)
    if (!fileUrl) {
      console.log('APP端：文件URL不存在，跳过压缩')
      resolve()
      return
    }

    const { quality = 0.8, maxWidth = 300, maxHeight = 300 } = options

    // 首先获取图片信息，计算等比压缩的尺寸
    uni.getImageInfo({
      src: fileUrl,
      success: (imageInfo) => {
        console.log('获取图片信息成功:', imageInfo)

        // 计算等比压缩的尺寸
        let targetWidth = imageInfo.width
        let targetHeight = imageInfo.height

        // 如果图片尺寸超过限制，进行等比缩放
        if (targetWidth > maxWidth || targetHeight > maxHeight) {
          const scale = Math.min(maxWidth / targetWidth, maxHeight / targetHeight)
          targetWidth = Math.round(targetWidth * scale)
          targetHeight = Math.round(targetHeight * scale)
          console.log('APP端等比压缩尺寸:', targetWidth, 'x', targetHeight)
        } else {
          console.log('APP端图片尺寸在限制范围内，无需缩放')
        }

        // 使用计算后的尺寸进行压缩
        uni.compressImage({
          src: fileUrl,
          quality: Math.floor(quality * 100), // 转换为百分比
          width: targetWidth.toString(),
          height: targetHeight.toString(),
          success: (res) => {
            console.log('APP压缩成功:', res)

            // 更新文件路径为压缩后的路径
            file.path = res.tempFilePath
            file.url = res.tempFilePath

            console.log('APP端更新后的文件:', file)
            resolve()
          },
          fail: (error) => {
            console.error('APP压缩失败:', error)
            // 压缩失败，使用原文件
            resolve()
          },
        })
      },
      fail: (error) => {
        console.error('获取图片信息失败:', error)
        // 获取图片信息失败，使用默认压缩
        uni.compressImage({
          src: fileUrl,
          quality: Math.floor(quality * 100),
          width: maxWidth.toString(),
          height: maxHeight.toString(),
          success: (res) => {
            console.log('APP默认压缩成功:', res)
            file.path = res.tempFilePath
            file.url = res.tempFilePath
            resolve()
          },
          fail: (error) => {
            console.error('APP默认压缩也失败:', error)
            resolve()
          },
        })
      },
    })
  })
}

// H5端压缩方法
const compressImageInH5 = async (file: any, options: CompressOptions): Promise<void> => {
  try {
    console.log('H5端开始压缩:', file)

    // 使用现有的compressImage方法
    const compressedFile = await compressImage(file, options)

    console.log('H5压缩完成:', compressedFile)

    // 更新文件对象
    Object.assign(file, {
      url: compressedFile.url,
      path: compressedFile.url,
      size: compressedFile.size,
      compressed: true,
    })

    console.log('H5端更新后的文件:', file)
  } catch (error) {
    console.error('H5压缩失败:', error)
    // 压缩失败，保持原文件不变
  }
}

/**
 * 创建before-upload处理函数的工厂方法
 * @param {Object} options - 压缩选项
 * @returns {Function} before-upload处理函数
 */
export const createBeforeUploadHandler = (
  options: CompressOptions & {
    showLoading?: boolean
    loadingText?: string
  } = {},
) => {
  return (params: { files: any[]; resolve: (value: boolean) => void }) => {
    return compressForUpload(params, options)
  }
}

/**
 * 预设配置 - 不同场景的推荐压缩参数
 */
export const COMPRESS_PRESETS = {
  // 头像压缩（小尺寸，高质量）
  avatar: {
    quality: 0.8,
    maxWidth: 400,
    maxHeight: 400,
    maxSize: 20 * 1024 * 1024, // 10MB
    showLoading: true,
    loadingText: '',
  },

  // 高质量压缩（大文件，高质量）
  highQuality: {
    quality: 0.8,
    maxWidth: 2000,
    maxHeight: 2000,
    maxSize: 20 * 1024 * 1024, // 20MB
    showLoading: true,
    loadingText: '',
  },
}

/**
 * 使用预设配置的便捷方法
 * @param {string} presetName - 预设名称
 * @param {Object} customOptions - 自定义选项（会覆盖预设值）
 * @returns {Function} before-upload处理函数
 */
export const createPresetHandler = (
  presetName: keyof typeof COMPRESS_PRESETS,
  customOptions: Partial<CompressOptions & { showLoading?: boolean; loadingText?: string }> = {},
) => {
  const preset = COMPRESS_PRESETS[presetName]
  const options = { ...preset, ...customOptions }
  return createBeforeUploadHandler(options)
}

/**
 * 快速创建常用场景的处理函数
 */
export const quickHandlers = {
  // 头像上传
  avatar: () => createPresetHandler('avatar'),
  // 高质量上传
  highQuality: () => createPresetHandler('highQuality'),
}
