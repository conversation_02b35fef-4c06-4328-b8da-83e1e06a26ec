/**
 * 支付道具ID枚举
 */
export enum PayPropId {
  // 基础权益 ID 范围 17-20
  BASIC_PROP_1 = 17,
  BASIC_PROP_2 = 18,
  BASIC_PROP_3 = 19,
  BASIC_PROP_4 = 20,
  // 高级权益 ID 范围 21-24
  ADVANCED_PROP_1 = 21,
  ADVANCED_PROP_2 = 22,
  ADVANCED_PROP_3 = 23,
  ADVANCED_PROP_4 = 24,
}

/**
 * 支付道具类型配置
 */
export const PAY_PROP_TYPE_CONFIG = {
  BASIC: {
    ids: [
      PayPropId.BASIC_PROP_1,
      PayPropId.BASIC_PROP_2,
      PayPropId.BASIC_PROP_3,
      PayPropId.BASIC_PROP_4,
    ],
    name: '基础权益',
  },
  ADVANCED: {
    ids: [
      PayPropId.ADVANCED_PROP_1,
      PayPropId.ADVANCED_PROP_2,
      PayPropId.ADVANCED_PROP_3,
      PayPropId.ADVANCED_PROP_4,
    ],
    name: '高级权益',
  },
} as const
