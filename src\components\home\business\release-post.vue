<template>
  <view
    :style="{ paddingTop: `${sysSafeAreaInsets.top}px` }"
    class="relative mt-20rpx pl-42rpx pr-52rpx flex items-center"
  >
    <view class="flex-1">
      <view class="relative" @tap="handleSearchPost">
        <view class="flex items-center gap-14rpx">
          <text class="c-#000000 text-48rpx font500">
            {{
              loading
                ? ''
                : truncateText(
                    releaseActivePost?.positionMarkName || releaseActivePost?.positionName || '',
                    6,
                  ) || '暂未发布岗位'
            }}
          </text>

          <text
            v-if="canSearchPost"
            :class="modelShow ? 'i-carbon-triangle-solid' : 'i-carbon-triangle-down-solid'"
            class="text-16rpx c-#000000"
          />
        </view>
        <view class="absolute left--20rpx bottom--10rpx">
          <wd-img :src="releasePostTitle" height="32rpx" width="28rpx" />
        </view>
      </view>
    </view>
    <view class="flex flex-col items-center gap-4rpx" @tap="handleReleasePost">
      <wd-img :src="releasePostIcon" height="74rpx" width="76rpx" />
      <!--      <text class="c-#000000 text-22rpx font-500 pr-4rpx">发布岗位</text>-->
    </view>
  </view>
</template>

<script lang="ts" setup>
import { hrPositionQueryOptionList } from '@/service/hrPosition'
import { DICT_IDS, EMIT_EVENT } from '@/enum'
import { useReleasePost } from '@/hooks/business/useReleasePost'
import type { hrPositionQueryOptionListInt } from '@/service/hrPosition/types'
import releasePostTitle from '@/static/home/<USER>/release-post-title.png'
import releasePostIcon from '@/static/home/<USER>/release-post-icon_1.png'
import { truncateText } from '@/utils/util'

const $emits = defineEmits<{
  (e: 'selectPost'): void
  (e: 'searchPost'): void
}>()
const modelShow = defineModel('show', {
  type: Boolean,
  default: false,
})
const { sysSafeAreaInsets } = useSystemInfo()
const { getDictLabel } = useDictionary()
const { releaseActivePost, releaseIsHavePost } = useReleasePost()
const { bool: loading, setFalse: setLoadingFalse } = useBoolean(true)
const positionTotal = ref(0)

const canSearchPost = computed(() => {
  return releaseIsHavePost.value && positionTotal.value > 1
})

function handleReleasePost() {
  uni.navigateTo({
    url: '/sub_business/pages/release/index',
  })
}

function handleSearchPost() {
  if (canSearchPost.value) {
    $emits('searchPost')
  }
}

async function fetchHrPositionQueryOptionList() {
  const { data } = await hrPositionQueryOptionList({
    page: 1,
    size: 1,
  })
  const { list, total } = data
  positionTotal.value = total
  const [first] = list
  refreshHomeList(first)
}

async function refreshHomeList(post: hrPositionQueryOptionListInt) {
  releaseActivePost.value = post ?? {}
  releaseActivePost.value.educationLabel = await getDictLabel(
    DICT_IDS.EDUCATION_REQUIREMENT,
    releaseActivePost.value.workEducational,
  )
  setLoadingFalse()
  $emits('selectPost')
}

async function reload() {
  await uni.$onLaunched
  await fetchHrPositionQueryOptionList()
}

uni.$on(EMIT_EVENT.REFRESH_PUBLISH_POSITION, reload)
onMounted(() => {
  reload()
})
onBeforeUnmount(() => {
  uni.$off(EMIT_EVENT.REFRESH_PUBLISH_POSITION)
})
</script>

<style lang="scss" scoped>
@keyframes fade-in-overlay {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fade-out-overlay {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes slide-down {
  from {
    height: 0;
    opacity: 0;
  }
  to {
    height: 500rpx;
    opacity: 1;
  }
}

@keyframes slide-up {
  from {
    height: 500rpx;
    opacity: 1;
  }
  to {
    height: 0;
    opacity: 0;
  }
}

.animate-fade-in {
  animation: fade-in-overlay 0.2s ease-out forwards;
}

.animate-fade-out {
  animation: fade-out-overlay 0.2s ease-in forwards;
}

.animate-slide-down {
  animation: slide-down 0.2s ease-out forwards;
}

.animate-slide-up {
  animation: slide-up 0.2s ease-in forwards;
}
</style>
