<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <component :is="useComponent.component" :key="`news-${useComponent.type}`" />
</template>

<script setup lang="ts">
import { USER_TYPE } from '@/enum'
import business from './module/business.vue'
import personal from './module/personal.vue'

interface UseComponent {
  type: USER_TYPE
  component: Component
}

defineOptions({
  name: 'News',
})

const { userIntel, isCompleteBigStep } = useUserInfo()
const userRoleComponents: UseComponent[] = [
  {
    type: USER_TYPE.APPLICANT,
    component: personal,
  },
  {
    type: USER_TYPE.HR,
    component: business,
  },
]
const useComponent = computed(() => {
  if (!isCompleteBigStep.value) {
    return userRoleComponents.find((item) => item.type === USER_TYPE.APPLICANT)
  }
  return userRoleComponents.find(
    (item) => item.type === (userIntel.value?.type ?? USER_TYPE.APPLICANT),
  )
})
uni.hideTabBar()
</script>

<style lang="scss" scoped></style>
