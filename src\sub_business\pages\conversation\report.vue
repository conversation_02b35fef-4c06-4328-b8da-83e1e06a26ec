<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '举报',
    navigationBarBackgroundColor: '#F0F0F0',
  },
}
</route>

<template>
  <z-paging :paging-style="pageStyle" layout-only safe-area-inset-bottom bottom-bg-color="#F0F0F0">
    <view class="px58rpx flex flex-col gap-50rpx mt-60rpx">
      <wd-textarea
        v-model="reportModel.content"
        placeholder="填写举报内容"
        :maxlength="300"
        custom-class="h-738rpx w-full rounded-20rpx"
      />
      <view class="flex flex-col">
        <wd-upload
          v-model:file-list="fileList"
          :limit="3"
          :upload-method="uploadMethod"
          reupload
          accept="image"
          :max-size="2 * 1024 * 1024"
          image-mode="aspectFill"
          custom-class=""
          :show-limit-num="false"
        />
        <text class="c-#333333 text-24rpx w-300rpx">
          最少上传1张举报内容图片 图片不得大于2MB大小。
        </text>
      </view>
    </view>
    <template #bottom>
      <view class="px84rpx pb70rpx">
        <wd-button
          custom-class="w-full !h-84rpx !bg-#075EFF"
          @click="handleSetRemark"
          :loading="reportBool"
        >
          <text class="c-#FFFFFF text-24rpx font500">
            {{ reportBool ? '提交中...' : '立即提交' }}
          </text>
        </wd-button>
      </view>
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import { CommonUtil, useToast } from 'wot-design-uni'
import { userReportComplaintAdd } from '@/service/userReportComplaint'
import { userReportComplaintAddDataInt } from '@/service/userReportComplaint/types'
import type { UploadFile } from 'wot-design-uni/components/wd-upload/types'
import type { uploadImgThrumInt } from '@/service/attachment/types'

const toast = useToast()
const { userRoleIsBusiness } = useUserInfo()
const { pageParams } = usePagePeriod<{ conversationId: string }>()
const { bool: reportBool, setTrue: reportBoolTrue, setFalse: reportBoolFalse } = useBoolean()
const { uploadMethod } = useUpload()
const { pageStyle } = usePaging({
  style: {
    background: '#F0F0F0',
  },
})
const reportModel = ref<userReportComplaintAddDataInt>({})
const fileList = ref<UploadFile[]>([])

const userInfo = computed(() => {
  const userInfo = uni.$UIKit.appUserStore.getUserInfoFromStore(pageParams.value.conversationId)
  return {
    name: userInfo?.name,
    id: pageParams.value.conversationId,
    avatar: userInfo?.avatar,
    presenceExt: userInfo?.presenceExt,
    isOnline: userInfo?.isOnline,
    ext: JSON.parse(userInfo?.ext || '{}') as Api.IM.UserBusinessExtInfo,
  }
})
const imCustomUserId = computed(() => {
  return userRoleIsBusiness.value ? userInfo.value?.ext?.cUserId : userInfo.value?.ext?.hrUserId
})
const handleSetRemark = CommonUtil.debounce(async () => {
  if (!reportModel.value.content?.trim()) {
    toast.show('请输入举报内容')
    return
  }
  reportModel.value.evidenceFile = fileList.value
    .map((item) => (JSON.parse(item.response as string) as uploadImgThrumInt)?.fileId)
    .filter(Boolean)
    .join(',')
  try {
    reportBoolTrue()
    await userReportComplaintAdd(
      {
        ...reportModel.value,
        toUserId: imCustomUserId.value,
      },
      {
        custom: {
          catch: true,
          loading: true,
        },
      },
    )
    toast.show('举报已提交，感谢您的反馈！')
    setTimeout(() => {
      reportBoolFalse()
      uni.navigateBack()
    }, 1500)
  } catch (error) {
    reportBoolFalse()
  }
}, 300)
</script>

<style lang="scss" scoped>
:deep(.wd-textarea) {
  .wd-textarea__value {
    height: 100%;
  }
  .wd-textarea__inner {
    height: 100%;
  }
}
</style>
