<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="设置"></CustomNavBar>
    </template>
    <view class="setting">
      <view class="setting-list flex-between" @click="goPermissionList">
        <view class="list-item-text text-32rpx">权限列表</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between" @click="goPersonalInfoList">
        <view class="list-item-text text-32rpx">个人信息收集清单</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between" @click="goSharingInfoList">
        <view class="list-item-text text-32rpx">第三方共享信息收集清单</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

// 权限列表
const goPermissionList = () => {
  uni.navigateTo({
    url: '/setting/permissionList/index',
  })
}

// 个人信息收集清单
const goPersonalInfoList = () => {
  uni.navigateTo({
    url: '/setting/PersonalInfoList/index',
  })
}

// 第三方共享信息收集清单
const goSharingInfoList = () => {
  uni.navigateTo({
    url: '/setting/sharingInfoList/index',
  })
}
</script>
<style scoped lang="scss">
.setting {
  padding: 20rpx 20rpx;
  // margin-bottom: 320rpx;
  .setting-list {
    padding: 30rpx 20rpx;

    .list-item-text {
      color: #333;
    }
  }
}
</style>
