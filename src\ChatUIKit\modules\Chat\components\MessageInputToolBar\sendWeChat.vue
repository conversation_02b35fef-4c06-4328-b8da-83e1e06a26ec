<template>
  <view class="flex justify-center" @tap="handleSendWeChat">
    <ItemContainer title="换微信" :icon-url="wxImg" />
  </view>
</template>

<script lang="ts" setup>
import { autorun } from 'mobx'
import { EMIT_EVENT } from '@/enum'
import { userGetWxCode } from '@/service/user'
import type { InputToolbarEvent } from '@/ChatUIKit/types/index'
import ItemContainer from './itemContainer.vue'
import wxImg from '@/ChatUIKit/static/message-custom/wx.png'

const toolbarInject = inject<InputToolbarEvent>('InputToolbarEvent')
const { userRoleIsBusiness } = useUserInfo()
const { sendCustomWeChatCodeMessage, getIMUserInfo, customCardInfo, getConversationInfo } =
  useIMConversation()
const extUserInfo = ref<Api.IM.UserBusinessExtInfo>({})

async function handleSendWeChat() {
  const userId = userRoleIsBusiness.value ? extUserInfo.value.cUserId : extUserInfo.value.hrUserId
  const { conversationId } = getConversationInfo.value
  if (!conversationId) {
    uni.showToast({
      title: '请稍后重试',
      icon: 'none',
    })
    return
  }
  const { data: wechatCode } = await userGetWxCode()
  if (!wechatCode) {
    uni.$emit(EMIT_EVENT.PROMPT_ADD_WECHAT)
    return
  }
  sendCustomWeChatCodeMessage(conversationId, {
    wechatCode,
    userId,
  })
  toolbarInject?.closeToolbar()
}

const unwatchUserInfo = autorun(() => {
  const { conversationId, conversationType } = getConversationInfo.value
  if (conversationType === 'singleChat') {
    const { ext } = getIMUserInfo(conversationId)
    extUserInfo.value = JSON.parse(ext || '{}') as Api.IM.UserBusinessExtInfo
  }
})
onUnmounted(() => {
  unwatchUserInfo()
})
</script>

<style lang="scss" scoped>
//
</style>
