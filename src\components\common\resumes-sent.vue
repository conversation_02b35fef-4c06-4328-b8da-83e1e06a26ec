<template>
  <wd-config-provider :themeVars="themeVars">
    <wd-popup
      v-model="modelShow"
      custom-class="h-88% rounded-t-40rpx"
      position="bottom"
      @click="handlePopupClick"
    >
      <z-paging ref="pagingRef" v-model="pageData" :fixed="false" layout-only @query="queryList">
        <template #top>
          <view
            class="m-[40rpx_36rpx_16rpx] flex flex-col gap-20rpx border-b-1px border-b-solid border-b-[#D8D8D8]"
          >
            <view>
              <text class="c-#333333 font-500 text-36rpx">一键投递</text>
              <wd-tooltip
                v-model="showTooltip"
                content="一天内最多投递15个意向岗位"
                placement="right"
                @change="handleTooltipChange"
                @click.stop
              >
                <wd-icon color="#000000" name="warning" size="18px" class="ml-20rpx"></wd-icon>
              </wd-tooltip>
              <view
                class="w-150rpx h-8rpx bg-gradient-to-l from-[#DDDCFF] to-[#FFC2C2] rounded-full"
              />
            </view>
            <wd-textarea
              v-model="greeting"
              :maxlength="30"
              auto-height
              custom-label-class="!mr-0"
              label-width="auto"
              placeholder="请输入招呼语"
              type="text"
            >
              <template #label>
                <text class="c-#888888 text-24rpx font-500">招呼语：</text>
              </template>
            </wd-textarea>
          </view>
        </template>
        <wd-checkbox-group v-model="positionIds" custom-class="px-36rpx mt-30rpx" size="large">
          <view
            v-for="(item, key) in pageData"
            :key="`position-${item.id}-${key}`"
            class="flex items-center gap-26rpx mb-40rpx"
            @click="handleSelectPosition(key)"
          >
            <wd-checkbox
              ref="positionIdsRef"
              :model-value="item.id"
              custom-class="!m-0 !flex"
              @click.stop
            />
            <view
              class="h-140rpx w-100% bg-white shadow-[8rpx_8rpx_33rpx_0rpx_rgba(0,0,0,0.1)] rounded-20rpx pl-34rpx pr-24rpx flex flex-col justify-center gap-8rpx"
            >
              <view class="flex items-center">
                <text class="flex-1 line-clamp-1 c-#333333 text-28rpx font-500">
                  {{ item.positionName }}
                </text>
                <text class="c-#FF8080 text-28rpx font-500">
                  {{ formatSalary(item.workSalaryBegin, item.workSalaryEnd, item.salaryMonths) }}
                </text>
              </view>
              <view class="flex items-center">
                <text class="flex-1 line-clamp-1 c-#666666 text-22rpx font-400">
                  {{ item.name }}
                </text>
                <view class="flex items-center">
                  <text class="c-#888888 text-22rpx font-400">{{ item.districtName }}</text>
                </view>
              </view>
            </view>
          </view>
        </wd-checkbox-group>
        <template #bottom>
          <view
            class="bg-white shadow-[0rpx_0rpx_40rpx_0rpx_rgba(0,0,0,0.1)] rounded-t-20rpx h-168rpx flex items-center px-36rpx"
          >
            <wd-checkbox v-model="toggleAllVal" size="large" />
            全选
            <view class="flex-1 flex justify-end">
              <wd-button
                :disabled="!positionIds.length"
                :loading="sendResumeBool"
                custom-class="!h-82rpx !w-432rpx !bg-gradient-to-l from-[#DDDCFF] to-[#FFC2C2]"
                @click="handleSentResumes"
              >
                <text v-if="sendResumeBool">投递中...</text>
                <text v-else class="c-#333333 text-28rpx font-500">
                  {{ positionIds.length ? `一键投递${positionIds.length}个岗位` : '请选择岗位' }}
                </text>
              </wd-button>
            </view>
          </view>
        </template>
      </z-paging>
    </wd-popup>
  </wd-config-provider>
</template>

<script lang="ts" setup>
import { useToast, useMessage, CommonUtil } from 'wot-design-uni'
import { formatSalary } from '@/utils'
import { positionInfoBatchSendPositionList } from '@/service/positionInfo'
import type {
  positionInfoBatchSendPositionListDataInt,
  positionInfoBatchSendPositionListInt,
} from '@/service/positionInfo/types'
import { sysUserCallQueryOne } from '@/service/sysUserCall'
import type { BatchSendMessageListInt } from '@/service/exchangeResumeRecord/types'
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import type { CheckboxExpose } from 'wot-design-uni/components/wd-checkbox/types'

interface propsInt {
  params: positionInfoBatchSendPositionListDataInt
}

const modelShow = defineModel('show', {
  default: false,
})
const props = defineProps<propsInt>()
const toast = useToast()
const message = useMessage()
const {
  bool: sendResumeBool,
  setFalse: setSendResumeFalse,
  setTrue: setSendResumeTrue,
} = useBoolean()
const { sendMassResumeMessage } = useIMConversation()
const { pagingRef, pageData } = usePaging<positionInfoBatchSendPositionListInt>()

const themeVars: ConfigProviderThemeVars = {
  inputCellLabelWidth: 'auto',
  inputColor: '#333333',
  checkboxMargin: '40rpx',
}
const positionIds = ref<number[]>([])
const positionIdsRef = ref<CheckboxExpose[]>([])
const greeting = ref('')
const showTooltip = ref(false)
const toggleAllVal = computed({
  get: () => {
    const allIds = pageData.value.map((item) => item.id)
    return (
      allIds.length > 0 &&
      allIds.length === positionIds.value.length &&
      allIds.every((id) => positionIds.value.includes(id))
    )
  },
  set: (newValue) => {
    const allIds = pageData.value.map((item) => item.id)
    positionIds.value = newValue ? allIds : []
  },
})
const sendList = computed<BatchSendMessageListInt[]>(() => {
  const havHxUserInfoVO = pageData.value.filter((item) => item?.hxUserInfoVO)
  return positionIds.value
    .map<BatchSendMessageListInt | null>((index) => {
      const foundItem = havHxUserInfoVO.find((item) => item.id === index && item?.hxUserInfoVO)
      if (!foundItem?.hxUserInfoVO) {
        console.warn(`未找到 positionId ${index} 对应的 hxUserInfoVO`)
        return null
      }
      const {
        hxUserInfoVO: { userId: hrUserId, username: to },
        id: positionId,
      } = foundItem
      return {
        to,
        hrUserId,
        positionId,
      }
    })
    .filter((item): item is BatchSendMessageListInt => item !== null)
})

function handleSelectPosition(key: number) {
  positionIdsRef.value[key].toggle()
}

function handleTooltipChange(event: { show: boolean }) {
  showTooltip.value = event.show
}

function handlePopupClick() {
  // 点击弹窗内容区域时关闭 tooltip
  if (showTooltip.value) {
    showTooltip.value = false
  }
}

const handleSentResumes = CommonUtil.debounce(async () => {
  if (!greeting.value) {
    toast.show('请填写招呼语')
    return
  }
  try {
    setSendResumeTrue()
    await sendMassResumeMessage({ greeting: greeting.value, sendList: sendList.value })
    positionIds.value = []
    pagingRef.value.reload()
    setSendResumeFalse()
  } catch (err) {
    console.log('err', err)
    message
      .confirm({
        title: '提示',
        msg: '请完善简历后再投递',
      })
      .then(() => {
        uni.navigateTo({
          url: '/resumeRelated/AttachmentResume/index',
        })
      })
      .catch()
    setSendResumeFalse()
  }
}, 300)

async function positionInfoBatchSendPositionListApi() {
  await nextTick()
  const { data } = await positionInfoBatchSendPositionList(props.params)
  const { data: greetingMeg } = await sysUserCallQueryOne()
  pagingRef.value.completeByNoMore(data, true)
  greeting.value = greetingMeg || '您好，非常期望加入贵公司，这是我的简历，希望能和您聊聊！'
}

function queryList() {
  positionInfoBatchSendPositionListApi()
}

watch(modelShow, (val) => {
  if (val) {
    positionInfoBatchSendPositionListApi()
  }
})
</script>

<style lang="scss" scoped>
//
:deep(.wd-tooltip__inner) {
  padding-right: 13rpx;
  font-size: 22rpx;
  text-align: left;
  white-space: normal;
  direction: ltr;
}
</style>
