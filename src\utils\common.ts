// 手机号中间隐藏
export function maskPhoneNumber(phoneNumber) {
  // 确保手机号码是11位
  if (phoneNumber.length === 11) {
    // 使用模板字符串进行替换
    return `${phoneNumber.slice(0, 3)}****${phoneNumber.slice(-4)}`
  } else {
    return phoneNumber // 如果不是11位，直接返回原号码
  }
}

// 获取姓名的第一个字
export function maskNaneFrist(data) {
  if (data) {
    return data.substring(0, 1)
  } else {
    return '我'
  }
}
// 获取最后2个字
export function getLastTwoCharsOrOne(str) {
  if (typeof str !== 'string' || str.length === 0) {
    return '' // 如果输入不是字符串或字符串为空，返回空字符串
  }

  if (str.length < 2) {
    return str // 如果字符串长度小于2，返回整个字符串
  } else {
    return str.slice(-2) // 否则返回最后两个字符
  }
}
// 年月日
export function formatDateDay() {
  const date = new Date()
  const y = date.getFullYear()
  let m = date.getMonth() + 1
  m = m < 10 ? '0' + m : m
  let d = date.getDate()
  d = d < 10 ? '0' + d : d
  return y + '-' + m + '-' + d
}
// 年月日
export function formatDateDayFmt() {
  const date = new Date()
  const y = date.getFullYear()
  let m = date.getMonth() + 1
  m = m < 10 ? '0' + m : m
  let d = date.getDate()
  d = d < 10 ? '0' + d : d
  return {
    year: y,
    month: m,
    day: d,
  }
}
// 年月日时分秒
export function formatDateTime() {
  const date = new Date()
  const y = date.getFullYear()
  let m = date.getMonth() + 1
  m = m < 10 ? '0' + m : m
  let d = date.getDate()
  d = d < 10 ? '0' + d : d
  let h = date.getHours()
  h = h < 10 ? '0' + h : h
  let minute = date.getMinutes()
  let second = date.getSeconds()
  minute = minute < 10 ? '0' + minute : minute
  second = second < 10 ? '0' + second : second
  return y + '-' + m + '-' + d + ' ' + h + ':' + minute + ':' + second
}
// 年月
export function formatDate() {
  const date = new Date()
  const y = date.getFullYear()
  let m = date.getMonth() + 1
  m = m < 10 ? '0' + m : m
  let d = date.getDate()
  d = d < 10 ? '0' + d : d
  return y + '-' + m
}

// 获取当月第一天
export function getFirstDayOfMonth() {
  const today = new Date() // 获取当前日期
  today.setDate(1) // 将日期设置为1，这样就会得到当前月份的第一天
  const y = today.getFullYear()
  let m = today.getMonth() + 1
  m = m < 10 ? '0' + m : m
  let d = today.getDate()
  d = d < 10 ? '0' + d : d
  return y + '-' + m + '-' + d
}
// 获取当月最后一天
export function getLastDayOfMonth() {
  // 获取当前日期
  const today = new Date()

  // 获取当前月份的最后一天，即下一个月的第一天往前推一天
  const nextMonthFirstDay = new Date(today.getFullYear(), today.getMonth() + 1, 1)
  const lastDayOfMonth = new Date(nextMonthFirstDay - 24 * 60 * 60 * 1000) // 减去一天的毫秒数

  // 格式化日期为 yyyy-mm-dd
  const year = lastDayOfMonth.getFullYear()
  const month = String(lastDayOfMonth.getMonth() + 1).padStart(2, '0') // 月份是从0开始的，所以加1
  const day = String(lastDayOfMonth.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}
// 获取当前周第一天的年月日
export function getFirstDayOfWeek() {
  const today = new Date()
  const dayOfWeek = today.getDay() // 获取当前是周几，0（周日）到6（周六）
  const firstDayOfWeek = new Date(today) // 创建一个新的Date对象，与当前日期相同

  // 计算周一的日期。如果是周日，需要减去1天；如果是周六，需要减去2天，依此类推。
  // 如果是周一，不需要任何操作；如果是周二，需要减去1天；依此类推。
  firstDayOfWeek.setDate(today.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1))

  // 格式化日期为年-月-日格式
  const year = firstDayOfWeek.getFullYear()
  const month = String(firstDayOfWeek.getMonth() + 1).padStart(2, '0') // 月份是从0开始的，所以要加1
  const date = String(firstDayOfWeek.getDate()).padStart(2, '0')

  return `${year}-${month}-${date}`
}

// 获取当年第一天的年月日
export function getFirstDayOfCurrentYear() {
  const now = new Date()
  const year = now.getFullYear()
  const firstDayOfYear = new Date(year, 0, 1) // 月份是从0开始的，所以0代表1月
  const y = firstDayOfYear.getFullYear()
  let m = firstDayOfYear.getMonth() + 1
  m = m < 10 ? '0' + m : m
  let d = firstDayOfYear.getDate()
  d = d < 10 ? '0' + d : d
  return y + '-' + m + '-' + d
}
// 获取当年最后一天

export function getLastDayOfYearAsString() {
  const currentYear = new Date().getFullYear()
  const lastDay = new Date(currentYear + 1, 0, 0) // 下一年1月0日即当年最后一天
  const year = lastDay.getFullYear()
  const month = String(lastDay.getMonth() + 1).padStart(2, '0')
  const day = String(lastDay.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}
// 上午好
export function getPreciseGreeting() {
  const now = new Date()
  const currentHour = now.getHours()
  const currentMinute = now.getMinutes()

  // 将时间转换为分钟数便于比较
  const minutes = currentHour * 60 + currentMinute

  if (minutes >= 5 * 60 && minutes < 12 * 60) {
    // 05:00 - 11:59
    return '上午好'
  } else if (minutes >= 12 * 60 && minutes < 14 * 60) {
    // 12:00 - 13:59
    return '中午好'
  } else if (minutes >= 14 * 60 && minutes < 18 * 60) {
    // 14:00 - 17:59
    return '下午好'
  } else if (minutes >= 18 * 60 && minutes < 24 * 60) {
    // 18:00 - 23:59
    return '晚上好'
  } else {
    // 00:00 - 04:59
    return '夜深了'
  }
}

// /navigate.js
export const safeRedirect = async (url, beforeJump) => {
  try {
    await beforeJump?.() // 执行前置操作
    uni.redirectTo({
      url,
    })
  } catch (error) {
    console.error('跳转被阻止:', error)
  }
}

// 获取昨日的年月日
export function getYesterdayDate() {
  // 创建日期对象并减去1天
  const date = new Date()
  date.setDate(date.getDate() - 1)

  // 获取年月日组件
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0') // 月份补零
  const day = String(date.getDate()).padStart(2, '0') // 日期补零

  // 组合成带分隔符的字符串
  return `${year}-${month}-${day}`
}
// 获取上月的第一天和最后一一天
export function getDatesOfLastMonth() {
  const today = new Date()
  // let firstDayOfLastMonth
  // let lastDayOfLastMonth

  // 获取上个月的第一天
  const firstDayOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1)

  // 获取上个月的最后一天
  // 方法1: 直接使用setDate(0)，这会跳转到上个月最后一天
  const lastDayOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0)

  // 格式化日期为 YYYY-MM-DD
  const formatDate = (date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0') // 月份是从0开始的
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  return {
    firstDay: formatDate(firstDayOfLastMonth),
    lastDay: formatDate(lastDayOfLastMonth),
  }
}
// 根据年月返回该月的第一天和最后一天
export function getMonthStartAndEnd(dateStr) {
  const [yearStr, monthStr] = dateStr.split('-')
  const year = parseInt(yearStr, 10)
  const month = parseInt(monthStr, 10)

  const firstDay = new Date(year, month - 1, 1)
  const lastDay = new Date(year, month, 0)

  const formatDate = (date) => {
    const y = date.getFullYear()
    const m = String(date.getMonth() + 1).padStart(2, '0')
    const d = String(date.getDate()).padStart(2, '0')
    return `${y}-${m}-${d}`
  }

  return {
    startDate: formatDate(firstDay),
    endDate: formatDate(lastDay),
  }
}
// 传入yyyy-mm，输出yyyy-mm-dd
export function getLastDayOfMonthDay(input) {
  console.log(input, 'input===')
  // 分割输入字符串为年和月，并转换为数字
  const [year, month] = input.split('-').map(Number)
  // 创建日期对象（月份从0开始，传入的month即实际月份，天数0表示上个月的最后一天）
  const lastDay = new Date(year, month, 0)

  // 格式化为yyyy-mm-dd
  const y = lastDay.getFullYear()
  const m = String(lastDay.getMonth() + 1).padStart(2, '0') // 月份补零
  const d = String(lastDay.getDate()).padStart(2, '0') // 日期补零
  console.log(`${y}-${m}-${d}`)
  return `${y}-${m}-${d}`
}
/**
 * 高级版时间格式化（支持自定义格式模板）
 * @param {number} timestamp 时间戳（毫秒）
 * @param {string} [format='YYYY-MM'] 格式模板
 * @returns {string} 格式化后的时间字符串
 */
export function formatTimestamp(timestamp, format = 'YYYY-MM') {
  const date = new Date(timestamp)
  if (isNaN(date.getTime())) return ''

  const replacements = {
    YYYY: date.getFullYear(),
    MM: String(date.getMonth() + 1).padStart(2, '0'),
    DD: String(date.getDate()).padStart(2, '0'),
    HH: String(date.getHours()).padStart(2, '0'),
    mm: String(date.getMinutes()).padStart(2, '0'),
    ss: String(date.getSeconds()).padStart(2, '0'),
  }

  return format.replace(/YYYY|MM|DD|HH|mm|ss/g, (match) => replacements[match])
}
// 金钱转换
// 金钱转换
export function numberTokw(num) {
  if (num < 1000) return num
  return Math.floor(num / 100) / 10 + 'k'
}
/**
 * 时间戳转换组件
 * @param {number|string} timestamp - 时间戳（支持秒或毫秒）
 * @param {string} format - 格式字符串，默认为'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的日期时间字符串
 */
export function formatTime(timestamp, format = 'YYYY-MM-DD HH:mm:ss') {
  // 确保timestamp是数字类型
  timestamp = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp

  // // 处理秒级时间戳（10位）
  // if (timestamp.toString().length === 10) {
  //   timestamp *= 1000
  // }

  const date = new Date(timestamp)

  // 如果时间戳无效，返回空字符串
  if (isNaN(date.getTime())) {
    return ''
  }

  const padZero = (num) => (num < 10 ? `0${num}` : num)

  const year = date.getFullYear()
  const month = padZero(date.getMonth() + 1)
  const day = padZero(date.getDate())
  const hours = padZero(date.getHours())
  const minutes = padZero(date.getMinutes())
  const seconds = padZero(date.getSeconds())

  // 替换格式字符串中的占位符
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}
// 当前日期向前推50年
export function get50YearsAgoTimestamp() {
  const date = new Date()
  date.setFullYear(date.getFullYear() - 50)
  return date.getTime() // 返回毫秒级时间戳
}
export function get50YearsAgoDate() {
  const date = new Date()
  date.setFullYear(date.getFullYear() + 50)
  return date
}
export function dateToTimestamp(dateInput) {
  // 如果已经是Date对象
  if (dateInput instanceof Date) {
    return dateInput.getTime()
  }

  // 如果是数字，假设已经是时间戳
  if (typeof dateInput === 'number') {
    return dateInput
  }

  // 如果是字符串，尝试解析
  if (typeof dateInput === 'string') {
    // 尝试常见格式
    const parsedDate = new Date(dateInput)
    if (!isNaN(parsedDate.getTime())) {
      return parsedDate.getTime()
    }

    // 处理特殊格式
    const formats = [
      { regex: /(\d{4})年(\d{1,2})月(\d{1,2})日/, replace: '$1/$2/$3' },
      { regex: /(\d{4})-(\d{2})-(\d{2})/, replace: '$1/$2/$3' },
      { regex: /(\d{2})\/(\d{2})\/(\d{4})/, replace: '$3/$1/$2' },
    ]

    for (const format of formats) {
      if (format.regex.test(dateInput)) {
        const normalized = dateInput.replace(format.regex, format.replace)
        const date = new Date(normalized)
        if (!isNaN(date.getTime())) {
          return date.getTime()
        }
      }
    }
  }

  throw new Error('无法解析的日期格式')
}

// 获取当前日期的前一周和后3周的日期和对应的星期，以数组格式返回
export function getMonthDays() {
  const today = new Date()
  const days = []

  // 计算前一周的开始日期（7天前）
  const oneWeekAgo = new Date(today)
  oneWeekAgo.setDate(today.getDate() - 7)

  // 计算后3周的结束日期（21天后）
  const threeWeeksLater = new Date(today)
  threeWeeksLater.setDate(today.getDate() + 21)

  // 从一周前开始遍历到三周后
  const startTime = oneWeekAgo.getTime()
  const endTime = threeWeeksLater.getTime()
  const dayInMs = 24 * 60 * 60 * 1000

  for (let time = startTime; time <= endTime; time += dayInMs) {
    const currentDate = new Date(time)
    const year = currentDate.getFullYear()
    const month = currentDate.getMonth() + 1
    const day = currentDate.getDate()

    const formattedDate = `${year}-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}`

    days.push({
      date: formattedDate,
      week: ['日', '一', '二', '三', '四', '五', '六'][currentDate.getDay()],
      isToday:
        currentDate.getFullYear() === today.getFullYear() &&
        currentDate.getMonth() === today.getMonth() &&
        currentDate.getDate() === today.getDate(),
    })
  }

  return days
}

// 将yyyy-mm转换为时间戳
export function getMonthStartTimestamp(dateStr) {
  const [year, month] = dateStr.split('-').map(Number)
  const date = new Date(year, month - 1, 1)
  return date.getTime()
}

// 获取上四年9月份的时间戳
export function getCurrentYear9MonthTimestamp() {
  const dateTime = new Date()
  const year = dateTime.getFullYear()
  const month = 9
  const date = new Date(year - 4, month - 1, 1)
  return date.getTime()
}
// 获取当年7月份的时间戳
export function getCurrentYear7MonthTimestamp() {
  const dateTime = new Date()
  const year = dateTime.getFullYear()
  const month = 7
  const date = new Date(year, month - 1, 1)
  return date.getTime()
}
// 当前年份向前推50年的9月的时间戳
export function get50YearsAgo9MonthTimestamp() {
  const dateTime = new Date()
  const year = dateTime.getFullYear()
  const month = 9
  const date = new Date(year - 50, month - 1, 1)
  return date.getTime()
}
// 当前年份向后推50年的7月的时间戳
export function get50YearsAgo7MonthTimestamp() {
  const dateTime = new Date()
  const year = dateTime.getFullYear()
  const month = 7
  const date = new Date(year + 50, month - 1, 1)
  return date.getTime()
}
// 时间戳转为年月，yyyy-mm
export function getYearMonthFromTimestamp(timestamp) {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  return `${year}-${month < 10 ? '0' + month : month}`
}

// 获取当前时间的小时和分钟
export function getCurrentHourAndMinute() {
  const date = new Date()
  const hour = date.getHours()
  const minute = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
  return { hour, minute }
}
