import { POST } from '../index'
import { payDealDetailInt, payDealListInt, payDealStatusInt } from './types'
import { HttpRequestConfig } from 'luch-request'

/** 订单信息 */
export const payDealList = (data: payDealListInt, config?: HttpRequestConfig) =>
  POST<any>('/easyzhipin-api/payDeal/queryAllList', data, config)
// 订单详情
export const payDealDetail = (data: payDealDetailInt, config?: HttpRequestConfig) =>
  POST<any>('/easyzhipin-api/payDeal/detail', data, config)

// 订单支付状态
export const payDealStatus = (data: payDealStatusInt, config?: HttpRequestConfig) =>
  POST<any>('/easyzhipin-api/pay/queryPayParam', data, config)
