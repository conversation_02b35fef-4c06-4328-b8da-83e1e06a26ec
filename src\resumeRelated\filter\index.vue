<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="page">
    <wd-navbar
      :bordered="false"
      custom-class="!bg-transparent"
      fixed
      leftArrow
      placeholder
      safeAreaInsetTop
      title="筛选"
      @click-left="handleClickLeft"
    ></wd-navbar>

    <!-- 分类容器 -->
    <view class="category-container">
      <!-- 左侧一级分类 -->
      <scroll-view
        :scroll-top="leftScrollTop"
        :scroll-with-animation="true"
        class="left-category"
        scroll-y
      >
        <view
          v-for="(item, index) in categories"
          :key="item.id"
          :class="{ active: currentCategory === index }"
          class="left-item"
          @click="changeCategory(index)"
        >
          {{ item.name }}
        </view>
      </scroll-view>

      <!-- 右侧二级分类 -->
      <scroll-view
        :scroll-into-view="scrollIntoViewId"
        :scroll-top="rightScrollTop"
        :scroll-with-animation="true"
        class="right-category"
        scroll-y
        @scroll="handleRightScroll"
      >
        <view
          v-for="(item, index) in categories"
          :id="'section_' + index"
          :key="item.id"
          class="right-section"
        >
          <!-- 二级分类标题 -->
          <view class="section-title">{{ item.name }}(单选)</view>

          <!-- 二级分类内容 -->
          <view class="section-content">
            <!-- 标签式分类 -->
            <template v-if="item.tags && item.tags.length > 0">
              <view class="tag-container">
                <view
                  v-for="(tag, tagIndex) in item.tags"
                  :key="tagIndex"
                  :class="{
                    myStyleBox: isTagSelected(item.id, tag),
                  }"
                  class="tag-item"
                  @click="
                    handleTagClick(item.id, tag, item.tagData ? item.tagData[tagIndex] : null)
                  "
                >
                  {{ tag }}
                </view>
              </view>
            </template>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 底部筛选栏 -->
    <view v-if="showFilterBar" class="filter-bar">
      <view class="filter-buttons">
        <button class="reset-btn" @click="resetFilters">重置</button>
        <button class="confirm-btn" @click="confirmFilters">确定({{ selectedTagCount }})</button>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { queryById } from '@/interPost/common'
import { numberTokw } from '@/utils/common'
import { useResumeStore } from '@/store'
import { DICT_IDS } from '@/enum/diction'

const resumeStore = useResumeStore()
const categories = ref<any[]>([])
const currentCategory = ref(0)
const leftScrollTop = ref(0)
const scrollIntoViewId = ref('section_0')
const rightScrollTop = ref(0)
const timer = ref<any>(null)
const selectedTags = ref<Record<string, any[]>>({})
const selectedTagIds = ref<Record<string, any[]>>({})
const showFilterBar = ref(true)
const rightScrollPositions = ref<number[]>([])
const isUserClick = ref(false) // 标记是否为用户主动点击
const type = ref('')
const selectedTagCount = computed(() => {
  let count = 0
  for (const key in selectedTags.value) {
    count += selectedTags.value[key].length
  }
  return count
})

const handleClickLeft = () => {
  uni.navigateBack()
}

// 设置默认选中"不限"选项
const setDefaultUnlimitedOptions = () => {
  categories.value.forEach((category) => {
    if (category.tagData && category.tagData.length > 0) {
      const unlimitedOption = category.tagData.find(
        (item: any) =>
          Object.values(item)[0] === '不限' || item.workYear === '不限' || item.salary === '不限',
      )
      if (unlimitedOption) {
        console.log(selectedTags.value, ' selectedTags.value')
        selectedTags.value[category.id] = ['不限']
        selectedTagIds.value[category.id] = [unlimitedOption]
      }
    }
  })
}

const initSelectedTagsFromStore = () => {
  const filterObj = resumeStore.fillterObg

  // 首先设置所有分类的默认"不限"选项
  setDefaultUnlimitedOptions()

  if (!filterObj) {
    // 如果没有筛选条件，保持默认的"不限"选项
    return
  }
  // 处理工作类型
  if (filterObj.jobType && categories.value.length > 0) {
    const jobTypeCategory = categories.value.find((c) => c.id === 'jobTypeList')
    if (jobTypeCategory && jobTypeCategory.tagData) {
      const selectedJobType = jobTypeCategory.tagData.find(
        (item: any) => Object.keys(item)[0] === String(filterObj.jobType),
      )
      if (selectedJobType) {
        const tagName = Object.values(selectedJobType)[0]
        selectedTags.value.jobTypeList = [tagName]
        selectedTagIds.value.jobTypeList = [selectedJobType]
      }
    }
  }

  // 处理学历要求
  if (filterObj.workEducational && categories.value.length > 0) {
    const eduCategory = categories.value.find((c) => c.id === 'workEducational')
    if (eduCategory && eduCategory.tagData) {
      const selectedEdu = eduCategory.tagData.find(
        (item: any) => Object.keys(item)[0] === String(filterObj.workEducational),
      )
      if (selectedEdu) {
        const tagName = Object.values(selectedEdu)[0]
        selectedTags.value.workEducational = [tagName]
        selectedTagIds.value.workEducational = [selectedEdu]
      }
    }
  }
  // 如果没有学历筛选条件，保持默认的"不限"选项（已在setDefaultUnlimitedOptions中设置）

  // 处理公司规模
  if (filterObj.sizeName && categories.value.length > 0) {
    const companyCategory = categories.value.find((c) => c.id === 'company')
    if (companyCategory && companyCategory.tagData) {
      const selectedCompany = companyCategory.tagData.find(
        (item: any) => Object.keys(item)[0] === filterObj.sizeName,
      )
      if (selectedCompany) {
        const tagName = Object.values(selectedCompany)[0]
        selectedTags.value.company = [tagName]
        selectedTagIds.value.company = [selectedCompany]
      }
    }
  }
  // 如果没有公司规模筛选条件，保持默认的"不限"选项（已在setDefaultUnlimitedOptions中设置）

  // 处理经验要求
  if (
    filterObj.workExperienceStart !== undefined &&
    filterObj.workExperienceStart !== null &&
    categories.value.length > 0
  ) {
    const expCategory = categories.value.find((c) => c.id === 'experience')
    if (expCategory && expCategory.tagData) {
      const selectedExp = expCategory.tagData.find(
        (item: any) =>
          Number(item.workExperienceStart) === Number(filterObj.workExperienceStart) &&
          Number(item.workExperienceEnd) === Number(filterObj.workExperienceEnd),
      )
      if (selectedExp) {
        const tagName = selectedExp.workYear
        selectedTags.value.experience = [tagName]
        selectedTagIds.value.experience = [selectedExp]
      }
    }
  }
  // 如果没有经验筛选条件，保持默认的"不限"选项（已在setDefaultUnlimitedOptions中设置）

  // 处理薪资范围
  if (filterObj.workSalaryBegin && categories.value.length > 0) {
    const salaryCategory = categories.value.find((c) => c.id === 'salary')
    if (salaryCategory && salaryCategory.tagData) {
      const selectedSalary = salaryCategory.tagData.find(
        (item: any) =>
          item.workSalaryBegin === filterObj.workSalaryBegin &&
          item.workSalaryEnd === filterObj.workSalaryEnd,
      )
      if (selectedSalary) {
        const beginSalary = numberTokw(selectedSalary.workSalaryBegin)
        const endSalary =
          selectedSalary.workSalaryEnd === 999999
            ? '及以上'
            : numberTokw(selectedSalary.workSalaryEnd)
        const tagName =
          endSalary === '及以上' ? beginSalary + endSalary : beginSalary + '-' + endSalary
        selectedTags.value.salary = [tagName]
        selectedTagIds.value.salary = [selectedSalary]
      }
    }
  }
  // 如果没有薪资筛选条件，保持默认的"不限"选项（已在setDefaultUnlimitedOptions中设置）
}
onLoad((options) => {
  type.value = options.type
})
onMounted(() => {
  initData()
})

const initData = async () => {
  if (type.value === 'toPerson') {
    await getCompany()
    await getExperiencel()
  }
  await getJobtype()
  await getSalary()
  await getEducational()

  rightScrollPositions.value = new Array(categories.value.length).fill(0)
  initSelectedTagsFromStore()
}
const getJobtype = async () => {
  const res: any = await queryById({ id: 106 }) // 使用工作类型对应的字典ID
  if (res.code === 0) {
    // 处理对象格式的数据 {0: '全部', 1: '全职', 2: '兼职', 3: '实习', ...}
    const dataArray = Object.entries(res.data).map(([key, value]) => ({
      [key]: value,
    }))

    // 过滤掉"全部"选项，因为我们有"不限"
    const filteredData = dataArray.filter((item) => {
      const value = Object.values(item)[0]
      return value !== '全部'
    })

    categories.value.push({
      id: 'jobTypeList',
      name: '工作类型',
      tags: ['不限', ...filteredData.map((v) => Object.values(v)[0])],
      tagData: [{ '': '不限' }, ...filteredData],
      multiSelect: false,
    })
  }
}
const getEducational = async () => {
  const res: any =
    type.value === 'toBusiness'
      ? await queryById({ id: DICT_IDS.EDUCATION_B })
      : await queryById({ id: DICT_IDS.EDUCATION_C })
  if (res.code === 0) {
    // 处理对象格式的数据 {0: '全部', 1: '高中及以下', 2: '专科', 3: '本科', 4: '硕士', 5: '博士及以上'}
    const dataArray = Object.entries(res.data).map(([key, value]) => ({
      [key]: value,
    }))

    // 过滤掉"全部"选项，因为我们有"不限"
    const filteredData = dataArray.filter((item) => {
      const value = Object.values(item)[0]
      return value !== '全部'
    })

    categories.value.push({
      id: 'workEducational',
      name: '学历要求',
      tags: [...filteredData.map((v) => Object.values(v)[0])],
      tagData: [...filteredData],
      multiSelect: false,
    })
  }
}

const getCompany = async () => {
  const res: any = await queryById({ id: 100 })
  if (res.code === 0) {
    // 处理对象格式的数据 {0: '全部', 1: '15人以下', 2: '15-50人', ...}
    const dataArray = Object.entries(res.data).map(([key, value]) => ({
      [key]: value,
    }))

    // 过滤掉"全部"选项，因为我们有"不限"
    const filteredData = dataArray.filter((item) => {
      const value = Object.values(item)[0]
      return value !== '全部'
    })

    categories.value.push({
      id: 'company',
      name: '公司规模',
      tags: ['不限', ...filteredData.map((v) => Object.values(v)[0])],
      tagData: [{ '': '不限' }, ...filteredData],
      multiSelect: false,
    })
  }
}

const getExperiencel = async () => {
  const res: any = await queryById({ id: 102 })
  if (res.code === 0) {
    categories.value.push({
      id: 'experience',
      name: '经验要求',
      tags: ['不限', ...res.data.map((v) => v.workYear)],
      tagData: [{ workExperienceStart: '', workExperienceEnd: '', workYear: '不限' }, ...res.data],
      multiSelect: false,
    })
  }
}

const getSalary = async () => {
  const res: any = await queryById({ id: 104 })
  if (res.code === 0) {
    res.data.forEach((v) => {
      const beginSalary = numberTokw(v.workSalaryBegin)
      const endSalary = v.workSalaryEnd === 999999 ? '及以上' : numberTokw(v.workSalaryEnd)
      if (endSalary === '及以上') {
        v.salary = beginSalary + endSalary
      } else {
        v.salary = beginSalary + '-' + endSalary
      }
    })
    categories.value.push({
      id: 'salary',
      name: '薪资范围',
      tags: ['不限', ...res.data.map((v) => v.salary)],
      tagData: [{ workSalaryBegin: '', workSalaryEnd: '', salary: '不限' }, ...res.data],
      multiSelect: false,
    })
  }
}

const changeCategory = (index: number) => {
  if (currentCategory.value === index) return

  // 标记为用户主动点击
  isUserClick.value = true
  currentCategory.value = index
  scrollIntoViewId.value = 'section_' + index

  nextTick(() => {
    const query = uni.createSelectorQuery()
    query.select('.left-item.active').boundingClientRect()
    query.select('.left-category').boundingClientRect()
    query.exec((res) => {
      if (res[0] && res[1]) {
        const activeItem = res[0]
        const container = res[1]
        const scrollTop =
          activeItem.top - container.top - (container.height - activeItem.height) / 2
        leftScrollTop.value = scrollTop
      }
    })
  })

  nextTick(() => {
    rightScrollTop.value = rightScrollPositions.value[index] || 0
    // 滚动完成后重置标志，延迟一点确保滚动完成
    setTimeout(() => {
      isUserClick.value = false
    }, 500)
  })
}

const handleRightScroll = (e: any) => {
  if (timer.value) clearTimeout(timer.value)

  timer.value = setTimeout(() => {
    const scrollTop = e.detail.scrollTop
    rightScrollPositions.value[currentCategory.value] = scrollTop

    // 如果是用户主动点击，不执行自动切换逻辑
    if (isUserClick.value) {
      return
    }

    const query = uni.createSelectorQuery()

    categories.value.forEach((item, index) => {
      query.select('#section_' + index).boundingClientRect()
    })

    query.exec((res) => {
      if (res && res.length > 0) {
        for (let i = 0; i < res.length; i++) {
          if (res[i] && res[i].top <= 100 && res[i].bottom > 100) {
            if (Math.abs(currentCategory.value - i) > 0) {
              currentCategory.value = i
            }
            rightScrollPositions.value[i] = scrollTop
            break
          }
        }
      }
    })
  }, 100)
}

const isTagSelected = (categoryId: string, tag: string) => {
  return selectedTags.value[categoryId] && selectedTags.value[categoryId].includes(tag)
}

const handleTagClick = (categoryId: string, tag: string, tagId: any) => {
  const category = categories.value.find((item) => item.id === categoryId)
  const isMultiSelect = category ? category.multiSelect : false

  if (!selectedTags.value[categoryId]) {
    selectedTags.value[categoryId] = []
    selectedTagIds.value[categoryId] = []
  }

  // 处理"不限"选项
  if (tag === '不限') {
    if (selectedTags.value[categoryId][0] === '不限') {
      selectedTags.value[categoryId] = []
      selectedTagIds.value[categoryId] = []
    } else {
      selectedTags.value[categoryId] = ['不限']
      selectedTagIds.value[categoryId] = [tagId]
    }
    return
  }

  // 如果已经选了"不限"，则先取消"不限"选项
  if (selectedTags.value[categoryId][0] === '不限') {
    selectedTags.value[categoryId] = []
    selectedTagIds.value[categoryId] = []
  }

  if (isMultiSelect) {
    const index = selectedTags.value[categoryId].indexOf(tag)
    if (index === -1) {
      selectedTags.value[categoryId].push(tag)
      selectedTagIds.value[categoryId].push(tagId)
    } else {
      selectedTags.value[categoryId].splice(index, 1)
      selectedTagIds.value[categoryId].splice(index, 1)
    }
  } else {
    if (selectedTags.value[categoryId][0] === tag) {
      selectedTags.value[categoryId] = []
      selectedTagIds.value[categoryId] = []
    } else {
      selectedTags.value[categoryId] = [tag]
      selectedTagIds.value[categoryId] = [tagId]
    }
  }

  if (selectedTags.value[categoryId].length === 0) {
    delete selectedTags.value[categoryId]
    delete selectedTagIds.value[categoryId]
  }
}

const removeTag = (categoryId: string, tag: string) => {
  if (selectedTags.value[categoryId]) {
    const index = selectedTags.value[categoryId].indexOf(tag)
    if (index !== -1) {
      selectedTags.value[categoryId].splice(index, 1)
      selectedTagIds.value[categoryId].splice(index, 1)

      if (selectedTags.value[categoryId].length === 0) {
        delete selectedTags.value[categoryId]
        delete selectedTagIds.value[categoryId]
      }
    }
  }
}

const clearAllTags = () => {
  selectedTags.value = {}
  selectedTagIds.value = {}

  // 重置后重新选择"不限"选项
  setDefaultUnlimitedOptions()

  const obj = {
    workSalaryBegin: '',
    workSalaryEnd: '',
    sizeName: '',
    workEducational: '',
    workExperienceStart: '',
    workExperienceEnd: '',
    jobType: '',
  }
  resumeStore.setfillterObg(obj)
}

const resetFilters = () => {
  clearAllTags()
}

const confirmFilters = () => {
  const obj = {
    workSalaryBegin: '',
    workSalaryEnd: '',
    sizeName: '',
    workEducational: '',
    jobType: '',
    workExperienceStart: '',
    workExperienceEnd: '',
  }
  console.log(selectedTagIds.value, 'selectedTagIds.value.salary=======')

  if (selectedTagIds.value.salary && selectedTagIds.value.salary[0]) {
    const salary = selectedTagIds.value.salary[0]
    if (salary.workSalaryBegin !== '' && salary.workSalaryEnd !== '') {
      obj.workSalaryBegin = salary.workSalaryBegin
      obj.workSalaryEnd = salary.workSalaryEnd
    }
  }

  if (selectedTagIds.value.jobTypeList && selectedTagIds.value.jobTypeList[0]) {
    const jobType = selectedTagIds.value.jobTypeList[0]
    if (Object.keys(jobType)[0] !== '') {
      obj.jobType = Object.keys(jobType)[0]
    }
  }
  if (selectedTagIds.value.company && selectedTagIds.value.company[0]) {
    const company = selectedTagIds.value.company[0]
    if (Object.keys(company)[0] !== '') {
      obj.sizeName = Object.keys(company)[0]
    }
  }

  if (selectedTagIds.value.workEducational && selectedTagIds.value.workEducational[0]) {
    const edu = selectedTagIds.value.workEducational[0]
    if (Object.keys(edu)[0] !== '') {
      obj.workEducational = Object.keys(edu)[0]
    }
  }

  if (selectedTagIds.value.experience && selectedTagIds.value.experience[0]) {
    const exp = selectedTagIds.value.experience[0]
    if (exp.workExperienceStart !== '' && exp.workExperienceEnd !== '') {
      obj.workExperienceStart = exp.workExperienceStart
      obj.workExperienceEnd = exp.workExperienceEnd
    }
  }

  resumeStore.setfillterObg(obj)
  uni.navigateBack({
    delta: 1,
    success() {
      if (type.value === 'toBusiness') {
        uni.$emit('refreshReleaseResume')
      } else {
        uni.$emit('refresh-a-page')
      }
    },
  })
}
</script>

<style lang="scss" scoped>
.page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.myStyleBox {
  position: relative;
  border: 1px solid #1160ff;
}

.myStyleBox::after {
  position: absolute;
  right: 0rpx;
  bottom: 0rpx;
  width: 32rpx;
  height: 28rpx;
  content: '';
  background-image: url('@/loginSetting/img/Mask_group(2).png');
  background-repeat: no-repeat; /* 防止平铺 */
  background-position: center; /* 保证居中显示 */
  background-size: cover; /* 让背景图完全覆盖容器 */
  border-radius: 10rpx 0 10rpx 0;
}

.category-container {
  display: flex;
  flex: 1;
  padding-bottom: 120rpx; /* 为底部筛选栏预留空间 */
  overflow: hidden;
}

.left-category {
  box-sizing: border-box;
  width: 160rpx;
  height: 100%;
  background-color: #f5f4f4;
}

.left-item {
  position: relative;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 104rpx;
  padding: 0 10rpx;
  font-size: 26rpx;
  line-height: 1.4;
  color: #333;
  text-align: center;
}

.left-item.active {
  font-weight: bold;
  color: #1160ff;
  background-color: #fff;
}

.left-item.active::before {
  position: absolute;
  top: 30rpx;
  bottom: 30rpx;
  left: 0;
  width: 6rpx;
  content: '';
  border-radius: 0 6rpx 6rpx 0;
}

.right-category {
  box-sizing: border-box;
  flex: 1;
  height: 100%;
  padding: 0 20rpx;
  background-color: #fff;
}

.section-title {
  height: 104rpx;
  padding-left: 10rpx;
  font-size: 28rpx;
  font-weight: 500;
  line-height: 104rpx;
  color: #333;
}

.section-content {
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.tag-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;
}

.tag-item {
  width: 45%;
  padding: 10rpx 24rpx;
  margin: 0 20rpx 20rpx 0;
  font-size: 26rpx;
  color: #333;
  text-align: center;
  background-color: #fff;
  border: 1rpx solid transparent;
  border-radius: 10rpx;
  box-shadow: 0 8rpx 28rpx 0 rgba(0, 0, 0, 0.1);
}

.filter-bar {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.filter-buttons {
  display: flex;
  padding: 20rpx 30rpx;
}

.reset-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  font-size: 30rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
}

.reset-btn {
  margin-right: 20rpx;
  color: #333;
  background-color: #f5f5f5;
}

.confirm-btn {
  background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
}
</style>
