<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="verifiCode bg-img">
    <CustomNavBar></CustomNavBar>
    <view class="verifiCode-main">
      <view class="verifiCode-title">「输入手机验证码」</view>
      <view class="verifiCode-input">
        <yi-code
          style="margin: auto"
          :width="500"
          :maxlength="4"
          @onChange="getNum"
          @onComplete="submit"
        ></yi-code>
      </view>

      <view class="verifiCode-subTit">
        <view class="verifiCode-subTit-1">收不到验证码?</view>
      </view>
      <view class="verifiCode-time">
        <view class="flex-c" style="justify-content: center" v-if="isShow">
          <wd-count-down
            :auto-start="false"
            format="ss"
            :time="time"
            ref="countDown"
            @change="changeTime"
            custom-class="custom-class"
          />
          <text class="codeText">后重新获取</text>
        </view>
        <view class="verifiCode-subTit" v-if="!isShow">
          <view class="codeText" @click="getCode">重新获取</view>
        </view>
      </view>
    </view>
    <common-link></common-link>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import CommonLink from '@/components/CommonLink/CommonLink.vue'
import yiCode from '@/uni_modules/yi-code/components/yi-code/yi-code.vue'
import { getLoginPhone, setToken, setInfo, setCheckInfo } from '@/utils/storage'
import { phonesendSms, subphoneLogin } from '@/interPost/login'
const { getPhoneInfo } = usePhoneVersion()
const { setUserIntel } = useUserInfo()
const { newInfoStepPage } = useNewInfoAll()

// 验证码
const smsCode = ref('')
// 协议
const isShow = ref(true)
// 防重复提交标志
const hasSubmitted = ref(false)

const countDown = ref(null)
// 时间
const time = ref(60 * 1000)

// 获取验证
const getNum = (e: any) => {
  smsCode.value = e
  // 当用户重新输入验证码时，重置提交标志
  if (e.length < 4) {
    hasSubmitted.value = false
  }
}
// 修改时间变化处理
const changeTime = (t: any) => {
  // 当剩余时间为0时停止
  if (t.seconds === 0 && t.minutes === 0) {
    stopCountdown()
  }
}
onLoad(() => {
  getCode()
})
const submit = async () => {
  if (smsCode.value.length !== 4) {
    uni.showToast({
      title: '请输入验证码',
      icon: 'none',
      duration: 3000,
    })
    return
  }

  // 防重复提交：如果已经提交过，直接返回
  if (hasSubmitted.value) {
    return
  }

  // 设置提交标志
  hasSubmitted.value = true
  const deviceInfo = await getPhoneInfo()
  const res: any = await subphoneLogin({
    phone: getLoginPhone(),
    smsCode: smsCode.value,
    deviceInfo,
    deviceModel: deviceInfo.deviceModel,
    deviceSystem: deviceInfo.osName,
    deviceType: deviceInfo.osVersion,
  })
  console.log(res, '验证码登陆成功')
  if (res.code === 0) {
    setToken(res.data.token)
    setInfo(res.data)
    setUserIntel(res.data)
    if (Object.prototype.hasOwnProperty.call(res.data, 'type')) {
      newInfoStepPage(false, res.data.requiredFinishStatus)
    } else {
      setCheckInfo({})
      uni.reLaunch({
        url: '/loginSetting/category/index',
      })
    }
  } else {
    // 提交失败时重置提交标志，允许重新提交
    hasSubmitted.value = false
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}

const getCode = async () => {
  try {
    isShow.value = true
    await nextTick()

    // 确保倒计时组件已挂载
    if (!countDown.value) {
      throw new Error('倒计时组件未加载')
    }

    // 重置并启动倒计时
    countDown.value.reset()
    countDown.value.start()

    // 发送验证码请求
    const res: any = await phonesendSms({
      captchaVerifyParam: null,
      imei: 'hfh38737',
      phone: getLoginPhone(),
    })

    if (res.code !== 0) {
      stopCountdown()
      uni.showToast({
        title: res.msg,
        icon: 'none',
      })
    }
  } catch (err) {
    stopCountdown()
    uni.showToast({
      title: '请求失败，请重试',
      icon: 'none',
    })
  }
}
// 停止倒计时的公共方法
const stopCountdown = () => {
  isShow.value = false
  if (countDown.value) {
    countDown.value.pause()
  }
}

onUnload(() => {
  stopCountdown()
})
</script>

<style lang="scss" scoped>
.codeText {
  font-size: 24rpx;
  color: $uni-color-primary;
}
.custom-class {
  font-size: 24rpx;
  color: $uni-color-primary;
}
.verifiCode {
  .verifiCode-main {
    padding-top: 240rpx;
    text-align: center;

    .verifiCode-title {
      font-size: #252525;
      font-size: 38rpx;
      font-weight: 500;
    }

    .verifiCode-input {
      width: 560rpx;
      margin: auto;
      margin-top: 80rpx;
    }

    .verifiCode-subTit {
      .verifiCode-subTit-1 {
        width: 220rpx;
        padding-top: 40rpx;
        margin: auto;
        font-size: 28rpx;
        color: #777777;
      }
    }

    .verifiCode-time {
      padding-top: 20rpx;
    }

    .verifiCode-btn {
      padding: 400rpx 44rpx 0rpx;
      text-align: center;

      .btn_box {
        box-sizing: border-box;
        width: 100%;
        padding: 0rpx 0rpx 0rpx;
        margin-top: 50rpx;

        .btn_bg {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 30rpx;
          font-size: 14px;
          font-weight: 500;
          color: #333333;
          background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
          border-radius: 14px 14px 14px 14px;
        }
      }
    }
  }
}
</style>
