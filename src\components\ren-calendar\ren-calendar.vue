<template>
  <view class="calendar-wrapper">
    <view class="header" v-if="headerBar">
      <view class="pre" @click="changeMonth('pre')">
        <wd-icon name="chevron-left" size="20px" color="#333"></wd-icon>
      </view>
      <view class="text-28rpx font-500">{{ y + '年' + formatNum(m) + '月' }}</view>
      <view class="next" @click="changeMonth('next')">
        <wd-icon name="chevron-right" size="22px" color="#333"></wd-icon>
      </view>
    </view>
    <view class="week">
      <view class="week-day" v-for="(item, index) in weekDay" :key="index">{{ item }}</view>
    </view>
    <view :class="{ hide: !monthOpen }" class="content" :style="{ height: height }">
      <view :style="{ top: positionTop + 'rpx' }" class="days">
        <view class="item" v-for="(item, index) in dates" :key="index">
          <view
            class="day"
            @click="selectOne(item, $event)"
            :class="{
              choose: choose === `${item.year}-${item.month}-${item.date}` && item.isCurM,
              nolm: !item.isCurM,
              today: isToday(item.year, item.month, item.date),
              isWorkDay: isWorkDay(item.year, item.month, item.date),
              // 新增：今天之前变灰
              'before-today': disabledBefore && isBeforeToday(item.year, item.month, item.date),
            }"
          >
            {{ Number(item.date) }}
          </view>
          <view
            class="markDay"
            v-if="isMarkDay(item.year, item.month, item.date) && item.isCurM"
          ></view>
          <!-- <view class="today-text" v-if="isToday(item.year, item.month, item.date)">今</view> -->
        </view>
      </view>
    </view>
    <image
      src="https://i.loli.net/2020/07/16/2MmZsucVTlRjSwK.png"
      mode="scaleToFill"
      v-if="collapsible"
      @click="toggle"
      class="weektoggle"
      :class="{ down: monthOpen }"
    ></image>
  </view>
</template>

<script>
export default {
  name: 'ren-calendar',
  props: {
    // 星期几为第一天(0为星期日)
    weekstart: {
      type: Number,
      default: 0,
    },
    // 标记的日期
    markDays: {
      type: Array,
      default: () => {
        return []
      },
    },
    // 是否展示月份切换按钮
    headerBar: {
      type: Boolean,
      default: true,
    },
    // 是否展开
    open: {
      type: Boolean,
      default: true,
    },
    // 是否可收缩
    collapsible: {
      type: Boolean,
      default: true,
    },
    // 未来日期是否不可点击
    disabledAfter: {
      type: Boolean,
      default: false,
    },
    // 是否可选择今天之前的日期
    disabledBefore: {
      type: Boolean,
      default: true,
    },
    // 默认选中的日期，格式：YYYY-MM-DD，不传则默认选中今天
    defaultDate: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      weektext: ['日', '一', '二', '三', '四', '五', '六'],
      y: new Date().getFullYear(), // 年
      m: new Date().getMonth() + 1, // 月
      dates: [], // 当前月的日期数据
      positionTop: 0,
      monthOpen: true,
      choose: '',
    }
  },
  created() {
    this.dates = this.monthDay(this.y, this.m)
    !this.open && this.toggle()
  },
  mounted() {
    console.log(this.defaultDate, 'this.defaultDate===============')
    // 如果有传入默认日期，使用传入的日期，否则使用今天
    if (this.defaultDate) {
      this.choose = this.defaultDate
      // 如果默认日期不是当前月，需要切换到对应月份
      const defaultDate = new Date(this.defaultDate)
      const defaultYear = defaultDate.getFullYear()
      const defaultMonth = defaultDate.getMonth() + 1
      if (defaultYear !== this.y || defaultMonth !== this.m) {
        this.changYearMonth(defaultYear, defaultMonth)
      }
    } else {
      this.choose = this.getToday().date
    }
  },
  computed: {
    // 顶部星期栏
    weekDay() {
      return this.weektext.slice(this.weekstart).concat(this.weektext.slice(0, this.weekstart))
    },
    height() {
      return (this.dates.length / 7) * 80 + 'rpx'
    },
  },
  methods: {
    formatNum(num) {
      const res = Number(num)
      return res < 10 ? '0' + res : res
    },
    getToday() {
      const date = new Date()
      const y = date.getFullYear()
      const m = date.getMonth()
      const d = date.getDate()
      const week = new Date().getDay()
      const weekText = ['日', '一', '二', '三', '四', '五', '六']
      const formatWeek = '星期' + weekText[week]
      const today = {
        date: y + '-' + this.formatNum(m + 1) + '-' + this.formatNum(d),
        week: formatWeek,
      }
      return today
    },
    // 获取当前月份数据
    monthDay(y, month) {
      const dates = []
      const m = Number(month)
      const firstDayOfMonth = new Date(y, m - 1, 1).getDay() // 当月第一天星期几
      const lastDateOfMonth = new Date(y, m, 0).getDate() // 当月最后一天
      const lastDayOfLastMonth = new Date(y, m - 2, 0).getDate() // 上一月的最后一天
      const weekstart = this.weekstart === 7 ? 0 : this.weekstart
      const startDay = (() => {
        // 周初有几天是上个月的
        if (firstDayOfMonth === weekstart) {
          return 0
        } else if (firstDayOfMonth > weekstart) {
          return firstDayOfMonth - weekstart
        } else {
          return 7 - weekstart + firstDayOfMonth
        }
      })()
      const endDay = 7 - ((startDay + lastDateOfMonth) % 7) // 结束还有几天是下个月的
      for (let i = 1; i <= startDay; i++) {
        dates.push({
          date: this.formatNum(lastDayOfLastMonth - startDay + i),
          day: weekstart + i - 1 || 7,
          month: m - 1 >= 0 ? this.formatNum(m - 1) : 12,
          year: m - 1 >= 0 ? y : y - 1,
        })
      }
      for (let j = 1; j <= lastDateOfMonth; j++) {
        dates.push({
          date: this.formatNum(j),
          day: (j % 7) + firstDayOfMonth - 1 || 7,
          month: this.formatNum(m),
          year: y,
          isCurM: true, // 是否当前月份
        })
      }
      for (let k = 1; k <= endDay; k++) {
        dates.push({
          date: this.formatNum(k),
          day: (lastDateOfMonth + startDay + weekstart + k - 1) % 7 || 7,
          month: m + 1 <= 11 ? this.formatNum(m + 1) : 0,
          year: m + 1 <= 11 ? y : y + 1,
        })
      }
      // console.log(dates);
      return dates
    },
    isWorkDay(y, m, d) {
      // 是否工作日
      const ymd = `${y}/${m}/${d}`
      const formatDY = new Date(ymd.replace(/-/g, '/'))
      const week = formatDY.getDay()
      if (week === 0 || week === 6) {
        return false
      } else {
        return true
      }
    },
    isFutureDay(y, m, d) {
      // 是否未来日期
      const ymd = `${y}/${m}/${d}`
      const formatDY = new Date(ymd.replace(/-/g, '/'))
      const showTime = formatDY.getTime()
      const curTime = new Date().getTime()
      if (showTime > curTime) {
        return true
      } else {
        return false
      }
    },
    // 标记日期
    isMarkDay(y, m, d) {
      let flag = false
      for (let i = 0; i < this.markDays.length; i++) {
        const dy = `${y}-${m}-${d}`
        if (this.markDays[i] === dy) {
          flag = true
          break
        }
      }
      return flag
    },
    isToday(y, m, d) {
      const checkD = y + '-' + m + '-' + d
      const today = this.getToday().date
      if (checkD === today) {
        return true
      } else {
        return false
      }
    },
    // 新增：判断是否在今天之前
    isBeforeToday(y, m, d) {
      const dateStr = `${y}-${m}-${d}`
      const date = new Date(dateStr.replace(/-/g, '/')).getTime()
      const today = new Date(this.getToday().date.replace(/-/g, '/')).getTime()
      return date < today
    },
    // 展开收起
    toggle() {
      this.monthOpen = !this.monthOpen
      if (this.monthOpen) {
        this.positionTop = 0
      } else {
        let index = -1
        this.dates.forEach((i, x) => {
          this.isToday(i.year, i.month, i.date) && (index = x)
        })
        this.positionTop = -((Math.ceil((index + 1) / 7) || 1) - 1) * 80
      }
    },
    // 点击回调
    selectOne(i, event) {
      const date = `${i.year}-${i.month}-${i.date}`
      const selectD = new Date(date).getTime()
      const curTime = new Date().getTime()
      const week = new Date(date).getDay()
      const weekText = ['日', '一', '二', '三', '四', '五', '六']
      const formatWeek = '星期' + weekText[week]
      const response = {
        date,
        week: formatWeek,
      }
      if (!i.isCurM) {
        // console.log('不在当前月范围内');
        return false
      }
      // 新增：今天之前不可选
      if (this.disabledBefore && this.isBeforeToday(i.year, i.month, i.date)) {
        return false
      }
      if (selectD > curTime) {
        if (this.disabledAfter) {
          console.log('未来日期不可选')
          return false
        } else {
          this.choose = date
          this.$emit('onDayClick', response)
        }
      } else {
        this.choose = date
        this.$emit('onDayClick', response)
      }
      console.log(response)
    },
    // 改变年月
    changYearMonth(y, m) {
      this.dates = this.monthDay(y, m)
      this.y = y
      this.m = m
    },
    changeMonth(type) {
      if (type === 'pre') {
        if (this.m + 1 === 2) {
          this.m = 12
          this.y = this.y - 1
        } else {
          this.m = this.m - 1
        }
      } else {
        if (this.m + 1 === 13) {
          this.m = 1
          this.y = this.y + 1
        } else {
          this.m = this.m + 1
        }
      }
      this.dates = this.monthDay(this.y, this.m)
    },
  },
}
</script>

<style lang="scss" scoped>
.calendar-wrapper {
  padding-bottom: 10rpx;
  font-size: 28rpx;
  color: #bbb7b7;
  text-align: center;
  background-color: #fff;

  .header {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 88rpx;
    font-size: 32rpx;
    font-weight: bold;
    color: #42464a;
    border-bottom: 2rpx solid #f2f2f2;
    .pre,
    .next {
      padding: 8rpx 15rpx;
      font-size: 28rpx;
      font-weight: normal;
      color: #333;
      //   border: 2rpx solid #dcdfe6;
      border-radius: 10rpx;
    }
    .pre {
      margin-right: 30rpx;
    }
    .next {
      margin-left: 30rpx;
    }
  }

  .week {
    display: flex;
    align-items: center;
    height: 80rpx;
    line-height: 80rpx;
    border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);

    view {
      flex: 1;
    }
  }

  .content {
    position: relative;
    overflow: hidden;
    transition: height 0.4s ease;

    .days {
      position: relative;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      transition: top 0.3s;

      .item {
        position: relative;
        display: block;
        width: calc(100% / 7);
        height: 80rpx;
        line-height: 80rpx;

        .day {
          display: inline-block;
          width: 60rpx;
          height: 60rpx;
          overflow: hidden;
          font-style: normal;
          line-height: 60rpx;
          vertical-align: middle;
          border-radius: 60rpx;

          &.choose {
            color: #fff;
            background-color: #ff7648;
          }

          &.nolm {
            color: #fff;
            opacity: 0.3;
          }
          // 新增：今天之前变灰色
          &.before-today {
            color: #ccc;
            cursor: not-allowed;
            // background: #f5f5f5;
          }
        }
        .isWorkDay {
          color: #42464a;
        }

        .notSigned {
          position: absolute;
          bottom: 0;
          left: 50%;
          width: 8rpx;
          height: 8rpx;
          font-style: normal;
          pointer-events: none;
          background: #fa7268;
          border-radius: 10rpx;
        }
        .today {
          color: #fff;
          background-color: #a8c0ff;
        }
        .workDay {
          position: absolute;
          bottom: 0;
          left: 50%;
          width: 8rpx;
          height: 8rpx;
          font-style: normal;
          pointer-events: none;
          background: #ff7648;
          border-radius: 10rpx;
        }
        .markDay {
          position: absolute;
          bottom: 0;
          left: 50%;
          width: 8rpx;
          height: 8rpx;
          font-style: normal;
          pointer-events: none;
          background: #fc7a64;
          border-radius: 10rpx;
        }
      }
    }
  }

  .hide {
    height: 80rpx !important;
  }

  .weektoggle {
    position: relative;
    bottom: -42rpx;
    width: 85rpx;
    height: 32rpx;
    &.down {
      bottom: 0;
      transform: rotate(180deg);
    }
  }
}
</style>
