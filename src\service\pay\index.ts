import { POST } from '../index'
import { payPrePayDataInt, payPrePayInt, payQueryDealStatusDataInt } from './types'
import { HttpRequestConfig } from 'luch-request'

/** 预下单(每次创建新订单)接口 */
export const payPrePay = (data: payPrePayDataInt, config?: HttpRequestConfig) =>
  POST<payPrePayInt>('/easyzhipin-api/pay/prePay', data, config)

/** 查询支付状态接口 */
export const payQueryDealStatus = (data: payQueryDealStatusDataInt, config?: HttpRequestConfig) =>
  POST<number>('/easyzhipin-api/pay/queryDealStatus', data, config)
