// 顶部导航栏+状态栏
export const setCustomBar = (data: number) => {
  uni.setStorageSync('customBar', data)
}
export const getCustomBar = () => {
  return uni.getStorageSync('customBar')
}
// 导航栏
export const setStatusBar = (data: number) => {
  uni.setStorageSync('statusBar', data)
}
export const getStatusBar = () => {
  return uni.getStorageSync('statusBar')
}
// 手机号
export const setLoginPhone = (data: any) => {
  uni.setStorageSync('phone', data)
}
export const getLoginPhone = () => {
  return uni.getStorageSync('phone')
}
// 加密列表
export const setMsgList = (data: any) => {
  uni.setStorageSync('msg_list', data)
  // return console.log('ceshi')
}
export const getMsgList = () => {
  return uni.getStorageSync('msg_list')
}
// token
export const setToken = (data: any) => {
  uni.setStorageSync('token', data)
}
export const getToken = () => {
  return uni.getStorageSync('token')
}
// 用户信息
export const setInfo = (data: any) => {
  uni.setStorageSync('info', data)
}
export const getInfo = () => {
  return uni.getStorageSync('info')
}
export const setCheckInfo = (data: any) => {
  uni.setStorageSync('infoCheack', data)
}
export const getCheackInfo = () => {
  return uni.getStorageSync('infoCheack')
}
export const setCode = (data: any) => {
  uni.setStorageSync('code', data)
}
export const getCode = () => {
  return uni.getStorageSync('code')
}
export const setPhone = (data: any) => {
  uni.setStorageSync('phone', data)
}
export const getPhone = () => {
  return uni.getStorageSync('phone')
}

export const setDep = (data: any) => {
  uni.setStorageSync('dep', data)
}
export const getDep = () => {
  return uni.getStorageSync('dep')
}
export const setStaffId = (data: any) => {
  uni.setStorageSync('staffId', data)
}
export const getStaffId = () => {
  return uni.getStorageSync('staffId')
}

export const getManagerId = () => {
  return uni.getStorageSync('managerId')
}
export const setManagerId = (data: any) => {
  uni.setStorageSync('managerId', data)
}
export const getDepId = () => {
  return uni.getStorageSync('depId')
}
export const setDepId = (data: any) => {
  uni.setStorageSync('depId', data)
}
export const getHeaderImg = () => {
  return uni.getStorageSync('headerImg')
}
export const setHeaderImg = (data: any) => {
  uni.setStorageSync('headerImg', data)
}
export const setUserHasShownAgreement = (val: boolean) => {
  uni.setStorageSync('hasShownAgreement', val)
}
export const getUserHasShownAgreement = () => {
  const hasShownAgreement = uni.getStorageSync('hasShownAgreement')
  return typeof hasShownAgreement === 'boolean' ? hasShownAgreement : false
}
// 清除缓存
export const clearStorageSync = () => {
  uni.removeStorageSync('phone')
  uni.removeStorageSync('token')
  uni.removeStorageSync('msg_list')
  uni.removeStorageSync('info')
  uni.removeStorageSync('infoCheack')
  uni.removeStorageSync('user')
  uni.removeStorageSync('login')
  uni.removeStorageSync('resume')
  uni.removeStorageSync('Z-PAGING-REFRESHER-TIME-STORAGE-KEY')
}
