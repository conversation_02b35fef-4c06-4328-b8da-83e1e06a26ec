/**
 * 隐私协议处理工具
 * 用于处理 Android 隐私弹窗中的链接跳转
 */

/**
 * 处理隐私协议链接
 * @param url 链接地址
 */
export function handlePrivacyLink(url: string) {
  // 如果是外部链接，使用系统浏览器打开
  if (url.startsWith('http://') || url.startsWith('https://')) {
    // #ifdef APP-PLUS
    plus.runtime.openURL(url)
    // #endif

    // #ifdef H5
    window.open(url, '_blank')
    // #endif

    // #ifdef MP
    uni.navigateTo({
      url: `/pages/webview/index?url=${encodeURIComponent(url)}`,
    })
    // #endif
    return
  }

  // 如果是应用内页面，使用 uni.navigateTo 跳转
  if (url.startsWith('/')) {
    uni.navigateTo({
      url,
      fail: (err) => {
        console.error('页面跳转失败:', err)
        // 如果跳转失败，尝试使用外部链接
        const externalUrl = getExternalUrl(url)
        if (externalUrl) {
          handlePrivacyLink(externalUrl)
        }
      },
    })
    return
  }

  // 如果是相对路径，添加前缀
  if (!url.startsWith('/')) {
    const fullUrl = `/${url}`
    handlePrivacyLink(fullUrl)
  }
}

/**
 * 获取外部链接
 * @param internalUrl 内部链接
 * @returns 外部链接
 */
function getExternalUrl(internalUrl: string): string | null {
  const urlMap: Record<string, string> = {
    '/setting/PrivacyAgreement/ResumeUser': 'https://www.easyzhipin.com/#/agreementRules',
    '/setting/PrivacyAgreement/PrivacyPolicy': 'https://www.easyzhipin.com/#/privacyPolicy',
    'setting/PrivacyAgreement/ResumeUser': 'https://www.easyzhipin.com/#/agreementRules',
    'setting/PrivacyAgreement/PrivacyPolicy': 'https://www.easyzhipin.com/#/privacyPolicy',
  }

  return urlMap[internalUrl] || null
}

/**
 * 检查是否为隐私协议链接
 * @param url 链接地址
 * @returns 是否为隐私协议链接
 */
export function isPrivacyLink(url: string): boolean {
  const privacyUrls = [
    '/setting/PrivacyAgreement/ResumeUser',
    '/setting/PrivacyAgreement/PrivacyPolicy',
    'setting/PrivacyAgreement/ResumeUser',
    'setting/PrivacyAgreement/PrivacyPolicy',
    'https://www.easyzhipin.com/#/agreementRules',
    'https://www.easyzhipin.com/#/privacyPolicy',
  ]

  return privacyUrls.some((privacyUrl) => url.includes(privacyUrl))
}
