<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '使用记录',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging :paging-style="pageStyle" v-model="pageData" @query="queryList" ref="pagingRef">
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          left-arrow
          :bordered="false"
          safe-area-inset-top
          custom-class="px-25rpx"
          @click-left="handleClickLeft"
        >
          <template #title>
            <text class="c-#333333 text-32rpx font500">订单中心</text>
          </template>
        </wd-navbar>
      </wd-config-provider>
      <view class="w-400rpx">
        <wd-tabs
          @change="handleChange"
          v-model="newsTabsStatus"
          :active-color="'#000'"
          :inactive-color="'#888888'"
          line-width="80rpx"
          line-height="10rpx"
          custom-class="custom-class"
        >
          <wd-tab
            v-for="(item, index) in newsTabsList"
            :key="item.name"
            :title="`${item.label}`"
            :name="index"
          />
        </wd-tabs>
      </view>
    </template>
    <view class="px-40rpx py-20rpx">
      <template v-for="item in pageData" :key="item.id">
        <orderList :item="item" />
      </template>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import { type ConfigProviderThemeVars, useToast, useMessage } from 'wot-design-uni'
import orderList from '@/sub_business/components/orderList.vue'
import { payDealList } from '@/service/order'
import { truncateText, getTimeDifference } from '@/utils/util'
const toast = useToast()
const { pageStyle, pageData, pageInfo, pageSetInfo, pagingRef } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

const pollTimer = ref<number | null>(null)
const {
  bool: showPaymentMethod,
  setFalse: setShowPaymentMethod,
  toggle: togglePaymentMethod,
} = useBoolean()
const list = {
  0: '未支付',
  1: '支付成功',
  2: '待退款',
  3: '部分退款',
  4: '全部退款',
  5: '已发货',
  6: '已完成',
  7: '已关闭',
}
interface PaymentMethod {
  icon: string
  name: string
  action: () => void
}
const params = ref({
  entity: {},
  orderBy: {},
  page: 1,
  size: 10,
})
// const paymentMethods = ref<PaymentMethod[]>([
//   {
//     icon: aliPay,
//     name: '支付宝',
//     action: () => handlePayment('alipay'),
//   },
//   {
//     icon: wechatPay,
//     name: '微信',
//     action: () => handlePayment('wxpay'),
//   },
// ])
// const handlePayment = async (provider: 'alipay' | 'wxpay') => {
//   setShowPaymentMethod()
//   const payPass = provider === 'wxpay' ? 0 : 1
//   toast.loading({
//     msg: '支付中...',
//     loadingType: 'outline',
//     duration: 0,
//     cover: true,
//   })
//   try {
//     const outTradeNo = await paymentPrePay({
//       provider,
//       payPass,
//       positionInfoId: paramsPositionId.value,
//       propType: 0,
//       propId: payPropId.value,
//       propCategoryId: selectedPropId.value,
//     })
//     // 开始轮询支付状态
//     await pollPaymentStatus(outTradeNo)
//   } catch (error) {
//     toast.close()
//   }
// }
/**
 * 轮询支付状态
//  * @param outTradeNo 订单号
//  */
// const pollPaymentStatus = async (outTradeNo: string) => {
//   const maxAttempts = 60
//   const interval = 2000
//   let attempts = 0
//   const checkStatus = async (): Promise<void> => {
//     attempts++
//     try {
//       const { data } = await payQueryDealStatus({
//         outTradeNo,
//       })
//       if (data) {
//         clearPollTimer()
//         toast.close()

//         toast.show('发布成功')
//         setTimeout(() => {
//           // uni.$emit(EMIT_EVENT.REFRESH_PUBLISH_POSITION)
//           // uni.redirectTo({ url: '/sub_business/pages/positionManage/index' })
//           // resetReleasePostModel()
//         }, 1500)

//         toast.show('购买成功')
//         setTimeout(() => {
//           uni.$emit(EMIT_EVENT.REFRESH_PROP)
//           uni.navigateBack()
//         }, 1500)
//         return
//       }
//       if (attempts < maxAttempts) {
//         console.log('attempts')
//       } else {
//         clearPollTimer()
//         toast.close()
//         message
//           .alert({
//             title: '提示',
//             msg: '支付状态确认超时，请到订单页面查看支付结果',
//             closeOnClickModal: false,
//             confirmButtonText: '知道了',
//           })
//           .then(() => {
//             // TODO: 跳转到订单页面
//           })
//       }
//     } catch (error) {
//       if (attempts < maxAttempts) {
//         pollTimer.value = setTimeout(() => checkStatus(), interval)
//       } else {
//         clearPollTimer()
//         toast.close()
//         toast.error({
//           msg: '网络异常',
//           duration: 2000,
//         })
//       }
//     }
//   }
//   pollTimer.value = setTimeout(() => checkStatus(), 1000)
// }

// /**
//  * 清除轮询定时器
//  */
// const clearPollTimer = () => {
//   if (pollTimer.value) {
//     clearTimeout(pollTimer.value)
//     pollTimer.value = null
//   }
// }
const queryList = async (page, size) => {
  pageSetInfo(page, size)
  const res = await payDealList({ ...params.value, page: pageInfo.page, size: pageInfo.size })
  if (res.code === 0) {
    res.data.list.forEach((item) => {
      item.dealStateName = list[item.dealState]
      if (!item.dealState) {
        item.finishTimeD = getTimeDifference(item.finishTime)
      }
    })
    pagingRef.value.complete(res.data.list)
  }
}
const handleChange = ({ index }) => {
  if (index === 0) {
    params.value.entity.dealState = null
  } else if (index === 1) {
    params.value.entity.dealState = 0
  } else if (index === 2) {
    params.value.entity.dealState = 6
  }
  pagingRef.value.reload()
}
onLoad(async (options) => {
  await uni.$onLaunched
  pagingRef.value.reload()
})
function handleClickLeft() {
  uni.navigateBack()
}
const themeVars: ConfigProviderThemeVars = {
  navbarBackground: 'transparent',
  navbarArrowSize: '40rpx',
  navbarColor: '#000',
}
const newsTabsStatus = ref(0)
const newsTabsList = ref([
  { name: '1', label: '全部' },
  { name: '2', label: '待付款' },
  { name: '3', label: '已完成' },
])
</script>

<style lang="scss" scoped>
:deep(.custom-class) {
  background-color: transparent !important;

  .wd-tabs__nav {
    background-color: transparent;
  }
}

:deep(.wd-tabs__line) {
  background: #ff4545 !important;
}
</style>
