<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging :paging-style="pageStyle" layout-only>
    <template #top>
      <CustomNavBar title="资格证书">
        <template #left>
          <wd-icon class="back-button" color="#000" name="arrow-left" size="20" @click="back" />
        </template>
      </CustomNavBar>
    </template>
    <view class="qualiCertificate" @click="uploadImg">
      <view class="qualiCertificate-btn">添加资格证书</view>
    </view>
    <view class="qualiCertificate-list">
      <view
        v-for="(item, index) in imgList"
        :key="index"
        class="qualiCertificate-item flex-between m-b-40rpx"
        @click="goCertificate(item.id, item)"
      >
        <view class="flex-c">
          <image :src="img" class="qualiCertificate-img m-r-10rpx"></image>
          <view class="text-28rpx c-#333">{{ item.certificate }}</view>
        </view>
        <view
          :class="item.status === 3 ? 'c-#0ea500' : item.status === 2 ? 'c-#ff483b' : ''"
          class="text-28rpx"
        >
          {{ handleDate(item.status) }}
        </view>
      </view>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import isEqual from 'lodash/isEqual'
import { resumeSkillCertificateUpdate, resumeCertificateQuery } from '@/interPost/resume'
import img from '@/resumeRelated/img/upLoad.png'

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const baseInfoId = ref(null)
const imgList = ref([])
// 上传图片
const uploadImg = () => {
  const attachmentId = ''
  const attachmentUrl = ''
  const certificate = ''
  uni.navigateTo({
    url: `/resumeRelated/onlineResume/certificate/index?baseInfoId=${baseInfoId.value}&isAdd=add&&attachmentId=${attachmentId}&attachmentUrl=${attachmentUrl}&certificate=${certificate}`,
  })
}
// 去编辑和删除图片
const goCertificate = (id, item) => {
  console.log(item, '=====')
  const certificate = item?.certificate ? item.certificate : ''
  const attachmentId = item?.attachmentId ? item.attachmentId : ''
  const attachmentUrl = item?.attachmentUrl ? item.attachmentUrl : ''
  uni.navigateTo({
    url: `/resumeRelated/onlineResume/certificate/index?baseInfoId=${baseInfoId.value}&id=${id}&certificate=${certificate}&isAdd=edit&attachmentId=${attachmentId}&attachmentUrl=${attachmentUrl}`,
  })
}
// 获取图片列表
const getList = async () => {
  await uni.$onLaunched
  const res: any = await resumeCertificateQuery({ id: baseInfoId.value })
  if (res.code === 0) {
    imgList.value = res.data
  }
  console.log(res, 'res====')
}

// 处理上传状态
const handleDate = (status: any) => {
  switch (status) {
    case 1:
      return '已上传'
    case 2:
      return '请修改'
    case 3:
      return '已通过'
    default:
      return ''
  }
}

onLoad(async (options) => {
  console.log(options.id, 'options.id')
  baseInfoId.value = options.id
})
onShow(() => {
  getList()
})
// 返回
const back = () => {
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.qualiCertificate {
  padding: 40rpx;

  .qualiCertificate-btn {
    width: 100%;
    height: 120rpx;
    font-size: 28rpx;
    font-weight: 500;
    line-height: 120rpx;
    color: #fff;
    text-align: center;
    background: rgba(83, 120, 255, 1);
    border-radius: 20rpx;
    box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);
  }
}

.qualiCertificate-list {
  padding: 0rpx 40rpx 40rpx;

  .qualiCertificate-item {
    width: 100%;
    height: 120rpx;
    padding: 0rpx 40rpx;
    font-weight: 500;
    line-height: 120rpx;
    text-align: center;
    background: #fff;
    border-radius: 20rpx;
    box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);

    .qualiCertificate-img {
      width: 50rpx;
      height: 50rpx;
    }
  }
}
</style>
