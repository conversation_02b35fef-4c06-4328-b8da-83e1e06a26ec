<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging ref="pagingRef" :paging-style="pageStyle" layout-only>
    <template #top>
      <CustomNavBar title="公司信息">
        <template #left>
          <wd-icon class="back-button" color="#000" name="arrow-left" size="20" @click="back" />
        </template>
      </CustomNavBar>
    </template>
    <view class="px-40rpx py-40rpx">
      <!-- 头像 -->
      <view
        :class="formData.companyLogoUrl ? 'py-20rpx' : ''"
        class="bg-#fff rounded-[10rpx] shadow-[4rpx_4rpx_16rpx_0_rgba(0,0,0,0.10)] m-b-40rpx px-40rpx"
      >
        <view class="flex justify-between items-center">
          <view class="c-#000 font-500 p-r-40rpx text-34rpx">公司Logo</view>
          <view class="flex-between flex items-center">
            <wd-upload
              v-model:file-list="fileList"
              :action="baseUrl"
              :before-upload="beforeUpload"
              :header="header"
              :limit="1"
              :max-size="20 * 1024 * 1024"
              accept="image"
              custom-class="custom-class-1"
              image-mode="aspectFill"
              reupload
              @success="successFun"
            >
              <wd-icon class="lh-84rpx" color="#000000" name="add-circle1" size="18px"></wd-icon>
            </wd-upload>
          </view>
        </view>
      </view>
      <!-- 公司基本信息 -->
      <view class="bg-#fff rounded-[10rpx] p-40rpx shadow-[4rpx_4rpx_16rpx_0_rgba(0,0,0,0.10)]">
        <view class="page_flex_left text-32rpx font-500 relative m-b-60rpx">公司信息</view>
        <view
          class="flex justify-between items-center m-b-30rpx border-b-1rpx border-b-solid border-b-[#D9D9D9] p-b-30rpx"
        >
          <view class="c-#000 text-34rpx font-500 font-500">公司全称</view>
          <view class="flex-between flex items-center">
            <view :class="formData.name ? 'c-#000' : 'c-#888'" class="text-32rpx m-r-6rpx">
              {{ formData.name ? truncateText(formData.name, 10) : '请输入公司全称' }}
            </view>
          </view>
        </view>
        <view
          class="flex justify-between items-center m-b-30rpx border-b-1rpx border-b-solid border-b-[#D9D9D9] p-b-30rpx"
        >
          <view class="c-#000 text-34rpx font-500">公司简称</view>
          <view class="flex-between flex items-center" @click="goShortCompany">
            <view :class="formData.shortName ? 'c-#000' : 'c-#888'" class="text-32rpx">
              {{ formData.shortName ? formData.shortName : '请输入公司简称' }}
            </view>
            <wd-icon color="#888888" name="chevron-right" size="18px"></wd-icon>
          </view>
        </view>
        <view
          class="flex justify-between items-center m-b-30rpx border-b-1rpx border-b-solid border-b-[#D9D9D9] p-b-30rpx"
        >
          <view class="c-#000 text-34rpx font-500">行业</view>
          <view class="flex-between flex items-center" @click="goIndustry">
            <view :class="formData.industryName ? 'c-#000' : 'c-#888'" class="text-32rpx">
              {{ formData.industryName ? formData.industryName : '请选择行业' }}
            </view>
            <wd-icon color="#888888" name="chevron-right" size="18px"></wd-icon>
          </view>
        </view>
        <view
          class="flex justify-between items-center m-b-30rpx border-b-1rpx border-b-solid border-b-[#D9D9D9] p-b-30rpx"
        >
          <view class="c-#000 text-34rpx font-500">人员规模</view>
          <view class="flex-between flex items-center">
            <wd-picker
              v-model="formData.sizeName"
              :columns="peopleList"
              custom-label-class="custom-label-class"
              custom-value-class="custom-value-class"
              label=""
              @confirm="confirmPeople"
            />
          </view>
        </view>
      </view>
      <!-- 公司福利 -->
      <view
        class="bg-#fff rounded-[10rpx] p-40rpx shadow-[4rpx_4rpx_16rpx_0_rgba(0,0,0,0.10)] m-t-40rpx"
      >
        <view class="page_flex_left text-32rpx font-500 relative m-b-60rpx">公司福利</view>

        <view
          class="flex justify-between items-center m-b-30rpx border-b-1rpx border-b-solid border-b-[#D9D9D9] p-b-30rpx"
        >
          <view class="c-#000 text-34rpx font-500">工作时间</view>
          <view class="flex-between flex items-center" @click="showTime">
            <view v-if="formData.workStartTime && formData.workEndTime" class="text-32rpx c-#000">
              {{ formData.workStartTime }}-{{ formData.workEndTime }}
            </view>
            <view v-else class="text-32rpx c-#888">请选择</view>
            <wd-icon color="#888888" name="chevron-right" size="18px"></wd-icon>
          </view>
        </view>
        <view
          class="flex justify-between items-center m-b-30rpx border-b-1rpx border-b-solid border-b-[#D9D9D9] p-b-30rpx"
        >
          <view class="c-#000 text-34rpx font-500">福利待遇</view>
          <view class="flex-between flex items-center" @click="goCompanyBenefit">
            <view
              :class="
                formData.companyWelfareList && formData.companyWelfareList.length > 0
                  ? 'c-#000'
                  : 'c-#888'
              "
              class="text-32rpx"
            >
              {{
                formData.companyWelfareList && formData.companyWelfareList.length > 0
                  ? `已添加${formData.companyWelfareList.length}项`
                  : '请添加福利待遇'
              }}
            </view>
            <wd-icon color="#888888" name="chevron-right" size="18px"></wd-icon>
          </view>
        </view>
      </view>
      <!-- 公司相册 -->
      <view
        class="bg-#fff rounded-[10rpx] p-40rpx shadow-[4rpx_4rpx_16rpx_0_rgba(0,0,0,0.10)] m-t-40rpx"
      >
        <view class="page_flex_left text-32rpx font-500 relative m-b-60rpx">公司相册</view>

        <view
          class="flex justify-between items-center m-b-30rpx border-b-1rpx border-b-solid border-b-[#D9D9D9] p-b-30rpx"
        >
          <view class="c-#000 text-34rpx font-500">公司照片</view>
          <view class="flex-between flex items-center" @click="goCompanyStyleList">
            <view
              :class="formData.companyStyleList?.length ? 'c-#000' : 'c-#888'"
              class="text-32rpx"
            >
              {{
                formData.companyStyleList?.length
                  ? `已上传${formData.companyStyleList?.length}张`
                  : '请上传公司相册'
              }}
            </view>
            <wd-icon color="#888888" name="chevron-right" size="18px"></wd-icon>
          </view>
        </view>
      </view>
      <!-- 公司介绍 -->
      <view
        class="bg-#fff rounded-[10rpx] p-40rpx shadow-[4rpx_4rpx_16rpx_0_rgba(0,0,0,0.10)] m-t-40rpx"
      >
        <view class="page_flex_left text-32rpx font-500 relative m-b-40rpx">公司介绍</view>

        <view
          class="flex justify-between items-center m-b-30rpx border-b-1rpx border-b-solid border-b-[#D9D9D9] p-b-30rpx"
        >
          <view class="flex-between flex items-center" @click="goCompanyProfile">
            <view :class="formData.profile ? 'c-#000' : 'c-#888'" class="text-32rpx line-clamp-2">
              {{ formData.profile ? formData.profile : '请输入公司简介' }}
            </view>
          </view>
          <wd-icon
            color="#888888"
            name="chevron-right"
            size="18px"
            @click="goCompanyProfile"
          ></wd-icon>
        </view>
      </view>
    </view>
  </z-paging>
  <wd-message-box />
  <tpf-time-range
    ref="time"
    :endDefaultTime="endDefaultTime"
    :endTime="endTime"
    :startDefaultTime="startDefaultTime"
    :startTime="startTime"
    @timeRange="timeRange"
  ></tpf-time-range>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { quickHandlers } from '@/utils/compressImage'
import { DICT_IDS } from '@/enum'
import { truncateText } from '@/utils/util'
import { useLoginStore } from '@/store'
import { useMessage } from 'wot-design-uni'
import { hrupdateSizeName, queryCompanyInfo, updateWorkTime, updateLogo } from '@/service/hrCompany'

const { getToken } = useUserInfo()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const fileList = ref([])
const startTime = ref('00:00')
const startDefaultTime = ref('08:00')
const endTime = ref('23:59')
const endDefaultTime = ref('18:00')
const time = ref()
const loginStore = useLoginStore()
const { getDictData } = useDictionary()
const message = useMessage()
const formData = ref({
  name: '',
  sizeName: '',
  shortName: '',
  industryName: '',
  industryCode: '',
  workEndTime: '',
  workStartTime: '',
  profile: '',
  companyWelfareList: [] as string[],
  companyLogoId: '',
  companyLogoUrl: '',
})
// 预览
const goPreview = () => {
  uni.navigateTo({
    url: '/resumeRelated/company/index',
  })
}
// 图片id
const fileIdObj = ref({
  fileId: '',
})
// 图片地址/attachment/uploadImgThum
const baseUrl = import.meta.env.VITE_SERVER_BASEURL + '/easyzhipin-api/attachment/uploadImgThum'
// 图片请求头
const header = computed(() => {
  return {
    token: getToken(),
  }
})
const back = () => {
  loginStore.setindustryObj({})
  uni.navigateBack()
}
// before-upload 处理函数 - 使用预设的头像压缩配置
const beforeUpload = quickHandlers.avatar()
// 图片上传成功
const successFun = ({ fileList }) => {
  const res = JSON.parse(fileList[0].response)
  console.log(res, 'res====')
  if (res.code === 0) {
    getimgInfo(res.data[0].fileId)
  }
  console.log(res, '成功的返回')
}
// 上传头像
const getimgInfo = async (fileId: any) => {
  console.log(fileId, 'fileId===')
  const res: any = await updateLogo({ companyLogoId: fileId })
  if (res.code === 0) {
    getCompanyInfo()
  }
}
// 人员规模
const peopleList = ref([])
const getDictDataList = async (dictId: string | number) => {
  const res = await getDictData(dictId)
  peopleList.value = Object.entries(res || res.data).map(([key, value]) => ({
    value: key,
    label: value,
  }))
}
// 人员规模上传
const confirmPeople = async ({ value }) => {
  const res: any = await hrupdateSizeName({ sizeName: value })
  if (res.code === 0) {
    getCompanyInfo()
  }
}
// 获取公司列表信息
const getCompanyInfo = async () => {
  const res: any = await queryCompanyInfo()
  console.log(res, 'res====')
  if (res.code === 0) {
    formData.value = res.data
    startDefaultTime.value = res.data.workStartTime
    endDefaultTime.value = res.data.workEndTime

    if (res.data?.companyLogoUrl) {
      fileList.value = [
        {
          url: res.data.companyLogoUrl,
          name: 'tupian',
        },
      ]
    }
  }
}
// 公司相册
const goCompanyStyleList = () => {
  uni.navigateTo({
    url: '/sub_business/pages/company/model/companyStyleList',
  })
}
// 公司简称
const goShortCompany = () => {
  const shortName = formData.value.shortName ? formData.value.shortName : ''
  uni.navigateTo({
    url: `/sub_business/pages/company/model/shortCompany?shortName=${shortName}`,
  })
}
// 行業
const goIndustry = () => {
  // 处理行业数据（新增和编辑模式都需要）
  try {
    if (formData.value.industryName && formData.value.industryCode) {
      const obj = {
        industryCode: Number(formData.value.industryCode),
        industryName: formData.value.industryName,
      }
      loginStore.setindustryObj(obj)
    } else {
      loginStore.setindustryObj({
        industryName: '不限',
        industryCode: 0,
      })
    }
  } catch (error) {}
  uni.navigateTo({
    url: '/loginSetting/category/expPosition?needRequest=true',
  })
}
// 福利待遇
const goCompanyBenefit = () => {
  const companyWelfareList = formData.value.companyWelfareList
    ? formData.value.companyWelfareList
    : []
  uni.navigateTo({
    url: `/sub_business/pages/company/model/companyBenefit?companyWelfareList=${encodeURIComponent(
      JSON.stringify(companyWelfareList),
    )}`,
  })
}
const showTime = () => {
  time.value.open()
}
const timeRange = async (e: any) => {
  formData.value.workStartTime = e[0]
  formData.value.workEndTime = e[1]
  const res: any = await updateWorkTime({ workStartTime: e[0], workEndTime: e[1] })
  if (res.code === 0) {
    getCompanyInfo()
  }
}
onShow(() => {
  // 处理行业数据（新增和编辑模式都需要）
  const industryObj = loginStore.industryObj
  console.log(industryObj, 'industryObj====')
  if (industryObj?.industryName) {
    formData.value.industryName = industryObj.industryName
    formData.value.industryCode = industryObj.industryCode
  }
})
// 公司介绍
const goCompanyProfile = () => {
  const profile = formData.value.profile ? formData.value.profile : ''
  uni.navigateTo({
    url: `/sub_business/pages/company/model/companyProfile?profile=${profile}`,
  })
}
onLoad(async () => {
  await getDictDataList(DICT_IDS.PERSONNEL_SCALE)
  await getCompanyInfo()
})
uni.$on('industry-updated', getCompanyInfo)
uni.$on('welfare-updated', getCompanyInfo)
onUnmounted(() => {
  uni.$off('industry-updated', getCompanyInfo)
  uni.$off('welfare-updated', getCompanyInfo)
})
</script>

<style lang="scss" scoped>
:deep(.wd-picker__value) {
  font-size: 32rpx !important;
}
:deep(.wd-input) {
  width: 100%;
  text-align: right;
  background-color: transparent;
}

:deep(.wd-picker__arrow) {
  color: #888;
}

:deep(.wd-input__placeholder) {
  font-size: 24rpx !important;
  color: #888 !important;
}

:deep(.wd-input__inner) {
  font-size: 24rpx !important;
  font-weight: 500;
}

:deep(.wd-picker__cell) {
  padding: 0rpx;
}

:deep(.wd-picker__placeholder) {
  font-size: 24rpx;
  color: #888;
}

:deep(.wd-upload__close) {
  display: none;
}

:deep(.wd-picker__value) {
  margin-right: 0rpx;
  font-size: 24rpx;
}

::v-deep .custom-class-1 {
  display: flex;
  justify-content: center;
  width: 84rpx !important;
  height: 84rpx !important;

  margin: auto;
}

::v-deep .wd-upload__preview {
  width: 100% !important;
  height: 100% !important;
  margin: 0rpx !important;
  object-fit: contain !important;
  border-radius: 20rpx !important;
  box-shadow: 0 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
}

.btn-fixed {
  padding: 40rpx 80rpx;

  .btn_box {
    box-sizing: border-box;
    width: 100%;

    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 28rpx;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}

.page_flex_left::after {
  position: absolute;
  bottom: -8rpx;
  left: 0rpx;
  width: 128rpx;
  height: 10rpx;
  content: '';
  background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
  border-radius: 32rpx;
}
</style>
