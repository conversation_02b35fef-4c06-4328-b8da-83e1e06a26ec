<template>
  <template v-if="!userRoleIsBusiness">
    <view
      class="flex items-center c-#333333 text-28rpx my-30rpx bg-white rounded-[20rpx] h-156rpx px-54rpx gap-40rpx"
    >
      <view class="flex flex-col flex-justify-center gap-26rpx flex-1">
        <view class="flex items-center gap-16rpx">
          <wd-img :src="infoPosition" height="34rpx" width="34rpx" />
          <text class="line-clamp-1 flex-1">{{ customCardInfo.positionName }}</text>
        </view>
        <view class="flex items-center gap-16rpx">
          <wd-img :src="infoWelfare" height="34rpx" width="34rpx" />
          <text class="line-clamp-1 flex-1">
            {{ customCardInfo.positionBenefitList?.join(' · ') }}
          </text>
        </view>
      </view>
      <view class="flex flex-col flex-justify-center gap-26rpx flex-1">
        <view class="flex items-center gap-16rpx">
          <wd-img :src="infoCompanySalary" height="34rpx" width="34rpx" />
          <text class="line-clamp-1 flex-1">
            {{
              formatSalary(
                customCardInfo.workSalaryBegin,
                customCardInfo.workSalaryEnd,
                customCardInfo.salaryMonths,
                '月',
              )
            }}
          </text>
        </view>
        <view class="flex items-center gap-16rpx">
          <wd-img :src="infoCompanyAddress" height="34rpx" width="34rpx" />
          <text class="line-clamp-1 flex-1">
            {{ formatAddress }}
          </text>
        </view>
      </view>
    </view>
  </template>
</template>

<script lang="ts" setup>
import { formatSalary } from '@/utils'
import { positionInfoQueryIMCardInfoById } from '@/service/positionInfo'
import { hrResumeQueryIMCardInfoById } from '@/service/hrResume'
import type { hrResumeQueryIMCardInfoByIdInt } from '@/service/hrResume/types'
import infoPosition from '@/ChatUIKit/static/info-position.png'
import infoCompanyAddress from '@/ChatUIKit/static/info-company-address.png'
import infoCompanySalary from '@/ChatUIKit/static/info-company-salary.png'
import infoWelfare from '@/ChatUIKit/static/info-welfare.png'

interface propsInterface {
  ext: Api.IM.UserBusinessExtInfo
}

defineOptions({
  name: 'ChatInfoCard',
})

const props = withDefaults(defineProps<propsInterface>(), {})
const propsExt = computed(() => props.ext)

const { userIntel, getUserIsLogin, userRoleIsBusiness } = useUserInfo()
const { customCardInfo, customBusinessCardInfo } = useIMConversation()

const formatAddress = computed(() => {
  const { provinceName, cityName, districtName } = customCardInfo.value
  const isDirectMunicipality = provinceName === cityName
  const addressParts = isDirectMunicipality
    ? [provinceName, districtName]
    : [provinceName, cityName, districtName]
  return addressParts.filter(Boolean).join('')
})

async function fetchCardInfo() {
  try {
    const hrUserId = userRoleIsBusiness.value ? userIntel.value.userId : propsExt.value.hrUserId
    const userId = userRoleIsBusiness.value ? propsExt.value.cUserId : userIntel.value.userId
    if (userRoleIsBusiness.value) {
      const { data } = await hrResumeQueryIMCardInfoById(
        {
          userId,
        },
        {
          custom: {
            toast: true,
            catch: true,
          },
        },
      )
      if (data.infoThree) {
        const res = data.infoThree.split('·')
        if (res[1] === '0年') {
          data.infoThree = res[0]
        }
        if (data.infoTwo && data.infoTwo !== '面议') {
          data.infoTwo = data.infoTwo + '/月'
        }
      }
      customBusinessCardInfo.value = data

      customCardInfo.value.positionInfoId = data.positionId
    } else {
      const { data } = await positionInfoQueryIMCardInfoById(
        {
          userId,
          hrUserId,
        },
        {
          custom: {
            toast: true,
            catch: true,
          },
        },
      )
      customCardInfo.value = data
    }
  } catch (error) {
    console.error('Error fetching card info:', error)
  }
}

watch(
  [() => getUserIsLogin.value],
  ([login]) => {
    if (login) {
      fetchCardInfo()
    }
  },
  {
    immediate: true,
  },
)
</script>

<style lang="scss" scoped>
//
</style>
