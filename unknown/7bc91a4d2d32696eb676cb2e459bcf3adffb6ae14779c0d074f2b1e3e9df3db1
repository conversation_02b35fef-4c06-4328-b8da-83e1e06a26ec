.chat-wrap {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  background: linear-gradient(135deg, #ffc8c8 0%, #edf1ff 37%, #ffffff 100%);
  transition: height 0.2s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.chat-wrap-keyboard-close {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.msgs-wrap {
  flex: 1;
  position: relative;
  overflow: hidden;
  &_radius {
    border-radius: 40rpx 40rpx 0rpx 0rpx;
  }
}

.chat-input-wrap {
  padding: 32rpx 50rpx 60rpx;
  box-sizing: border-box;
  width: 100%;
  background-color: #ffffff;
  flex-shrink: 0;
}

.mask {
  position: absolute;
  height: 100%;
  width: 100vw;
  z-index: 88;
}
